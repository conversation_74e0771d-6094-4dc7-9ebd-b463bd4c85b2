2025-08-05 17:58:25.764867: currentXmlPath:D:\gdwork\code\a3g\packages\sea_socket\lib\src\protocols
2025-08-05 17:58:25.765841: 61
2025-08-05 17:58:26.398665: 开始初始化闸机协调器...
2025-08-05 17:58:26.398879: 串口 COM3 打开成功 (波特率: 9600)
2025-08-05 17:58:26.399525: 闸机串口服务初始化成功: COM3
2025-08-05 17:58:26.400037: 开始监听串口数据
2025-08-05 17:58:26.400037: 开始监听闸机串口命令
2025-08-05 17:58:26.400541: 闸机协调器初始化完成
2025-08-05 17:58:26.400541: 安全闸机系统初始化完成
2025-08-05 17:58:26.408551: 开始初始化MultiAuthManager...
2025-08-05 17:58:26.408551: 多认证管理器状态变更: initializing
2025-08-05 17:58:26.409541: 认证优先级管理器: 开始加载认证方式
2025-08-05 17:58:26.409541: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-05 17:58:26.409541: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-05 17:58:26.409541: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-05 17:58:26.409541: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-05 17:58:26.409541: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-05 17:58:26.409541: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-05 17:58:26.409541: 认证优先级管理器: 最终排序结果: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-05 17:58:26.409541: 认证优先级管理器: 主要认证方式: 微信扫码
2025-08-05 17:58:26.409541: 多认证管理器: 从优先级管理器加载的认证方式: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-05 17:58:26.409541: 多认证管理器: 当前默认显示方式: 微信扫码
2025-08-05 17:58:26.409541: 初始化二维码扫描认证服务
2025-08-05 17:58:26.410538: 初始化二维码扫码器
2025-08-05 17:58:26.410538: 二维码扫码器初始化完成
2025-08-05 17:58:26.410538: 二维码扫描认证服务初始化成功
2025-08-05 17:58:26.410538: 初始化共享二维码扫描认证服务
2025-08-05 17:58:26.410538: 微信扫码 认证服务初始化成功
2025-08-05 17:58:26.410538: 初始化读卡器认证服务
2025-08-05 17:58:26.410538: 读卡器认证服务初始化成功
2025-08-05 17:58:26.411516: 初始化共享读卡器认证服务
2025-08-05 17:58:26.411516: 读者证 认证服务初始化成功
2025-08-05 17:58:26.411516: 社保卡 认证服务初始化成功
2025-08-05 17:58:26.411516: 电子社保卡 认证服务初始化成功
2025-08-05 17:58:26.411516: 认证服务初始化完成，共初始化 4 种认证方式
2025-08-05 17:58:26.411516: 多认证管理器状态变更: idle
2025-08-05 17:58:26.411516: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:58:26.411516: MultiAuthManager初始化完成
2025-08-05 17:58:26.412511: 开始初始化SilencePageViewModel...
2025-08-05 17:58:26.412511: 闸机串口服务已经初始化
2025-08-05 17:58:26.412511: 开始初始化闸机认证服务...
2025-08-05 17:58:26.412511: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-05 17:58:26.412511: 开始初始化RFID服务...
2025-08-05 17:58:26.413529: socket 连接成功,isBroadcast:false
2025-08-05 17:58:26.413529: changeSocketStatus:true
2025-08-05 17:58:26.413529: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-05 17:58:26.413529: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-05 17:58:26.415503: dispose IndexPage
2025-08-05 17:58:26.415503: IndexPage dispose
2025-08-05 17:58:26.478067: Rsp : 941AY1AZFDFC
2025-08-05 17:58:26.495179: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-05 17:58:26.495179: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-05 17:58:26.495179: 发送心跳
2025-08-05 17:58:26.495179: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-05 17:58:26.513163: Rsp : 98YYYNNN00500320250805    1800012.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD52A
2025-08-05 17:58:26.913933: RFID服务初始化完成
2025-08-05 17:58:26.913933: 书籍API服务初始化完成: http://api.library.com
2025-08-05 17:58:26.914936: 串口监听已经启动
2025-08-05 17:58:26.914936: SilencePageViewModel初始化完成
2025-08-05 17:58:33.021070: 收到串口命令: enter_end (进馆结束)
2025-08-05 17:58:33.021070: 处理进馆结束
2025-08-05 17:58:33.021070: 闸机认证服务未在运行
2025-08-05 17:58:33.022069: 闸机状态变更: GateState.enterOver
2025-08-05 17:58:34.023558: 闸机认证服务未在运行
2025-08-05 17:58:34.023558: 闸机状态变更: GateState.idle
2025-08-05 17:58:34.023558: 页面状态变更: SilencePageState.welcome
2025-08-05 17:58:34.511284: 收到串口命令: exit_start (出馆开始)
2025-08-05 17:58:34.512277: 处理出馆开始
2025-08-05 17:58:34.512277: 闸机状态变更: GateState.exitStarted
2025-08-05 17:58:34.512277: 页面状态变更: SilencePageState.rfidScanning
2025-08-05 17:58:34.512277: 开始RFID扫描
2025-08-05 17:58:34.512277: RFID扫描已启动
2025-08-05 17:58:34.512277: 闸机状态变更: GateState.exitScanning
2025-08-05 17:58:35.713493: 模拟扫描到条码: BOOK213
2025-08-05 17:58:35.713493: 扫描到新条码: BOOK213 (总计: 1)
2025-08-05 17:58:35.713493: 页面状态变更: SilencePageState.rfidScanning
2025-08-05 17:58:38.113782: 模拟扫描到条码: BOOK651
2025-08-05 17:58:38.113782: 扫描到新条码: BOOK651 (总计: 2)
2025-08-05 17:58:38.113782: 页面状态变更: SilencePageState.rfidScanning
2025-08-05 17:58:40.090894: 收到串口命令: position_reached (到达指定位置)
2025-08-05 17:58:40.090894: 用户到达指定位置，准备停止RFID扫描
2025-08-05 17:58:40.090894: 闸机状态变更: GateState.exitChecking
2025-08-05 17:58:40.091891: 停止RFID扫描
2025-08-05 17:58:40.091891: RFID扫描已停止，共扫描到2个条码
2025-08-05 17:58:40.091891: RFID扫描结束，共扫描到2本书
2025-08-05 17:58:40.091891: 开始检查书籍状态，条码数量: 2
2025-08-05 17:58:41.314799: 页面状态变更: SilencePageState.exitAllowed
2025-08-05 17:58:41.314799: 发送闸机命令不完整: exit_open (7/8 bytes)
2025-08-05 17:58:44.316473: 闸机认证服务未在运行
2025-08-05 17:58:44.316473: 闸机状态变更: GateState.idle
2025-08-05 17:58:44.316473: 页面状态变更: SilencePageState.welcome
2025-08-05 17:58:45.521657: 收到串口命令: enter_start (进馆开始)
2025-08-05 17:58:45.521657: 处理进馆开始
2025-08-05 17:58:45.521657: 闸机状态变更: GateState.enterStarted
2025-08-05 17:58:45.521657: 页面状态变更: SilencePageState.authenticating
2025-08-05 17:58:45.521657: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:58:45.522693: 启动闸机认证服务...
2025-08-05 17:58:45.522693: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:58:45.522693: 开始启动MultiAuthManager监听...
2025-08-05 17:58:45.522693: 认证状态变化: MultiAuthState.listening
2025-08-05 17:58:45.522693: 认证状态变化: MultiAuthState.listening
2025-08-05 17:58:45.522693: 多认证管理器状态变更: listening
2025-08-05 17:58:45.522693: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:58:45.522693: 准备启动 2 个物理认证服务
2025-08-05 17:58:45.522693: 开始二维码扫描认证监听
2025-08-05 17:58:45.522693: 开始二维码扫描监听
2025-08-05 17:58:45.523693: 二维码扫描认证监听启动成功
2025-08-05 17:58:45.523693: 微信扫码 认证服务启动成功
2025-08-05 17:58:45.524642: 开始读卡器认证监听
2025-08-05 17:58:45.524642: 强制重新配置读卡器以确保状态一致性
2025-08-05 17:58:45.524642: 完全重置读卡器连接和监听器状态...
2025-08-05 17:58:45.524642: 已移除读卡器状态监听器
2025-08-05 17:58:45.524642: 已移除标签数据监听器
2025-08-05 17:58:45.524642: 所有卡片监听器已移除
2025-08-05 17:58:45.525639: stopInventory newPort:null
2025-08-05 17:58:45.726670: 发送 关闭 阅读器newPort:null
2025-08-05 17:58:45.726670: 读卡器连接已完全关闭
2025-08-05 17:58:45.727492: 读卡器连接和监听器状态已完全重置
2025-08-05 17:58:45.727492: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:45.727492: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:45.727492: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:45.728488: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:45.728488: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:45.728488: 添加有效设备: type=10, id=10
2025-08-05 17:58:45.728488: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:45.728488: 添加有效设备: type=13, id=13
2025-08-05 17:58:45.728488: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:45.728488: 添加有效设备: type=12, id=12
2025-08-05 17:58:45.729488: 总共加载了3个设备配置
2025-08-05 17:58:45.729488: changeReaders
2025-08-05 17:58:45.729488: createIsolate isOpen:false,isOpening:false
2025-08-05 17:58:45.730485: createIsolate newport null
2025-08-05 17:58:46.231872: open():SendPort
2025-08-05 17:58:46.231872: untilDetcted():SendPort
2025-08-05 17:58:46.232895: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:46.232895: 已移除读卡器状态监听器
2025-08-05 17:58:46.232895: 已移除标签数据监听器
2025-08-05 17:58:46.232895: 所有卡片监听器已移除
2025-08-05 17:58:46.232895: 已添加读卡器状态监听器
2025-08-05 17:58:46.232895: 已添加标签数据监听器
2025-08-05 17:58:46.233894: 开始监听卡片数据 - 所有监听器已就绪
2025-08-05 17:58:46.233894: 读卡器认证监听启动成功
2025-08-05 17:58:46.233894: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-05 17:58:46.233894: 所有认证服务启动完成，成功启动 2 个服务
2025-08-05 17:58:46.233894: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-05 17:58:46.233894: MultiAuthManager启动监听成功
2025-08-05 17:58:46.233894: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-05 17:58:46.233894: 闸机状态变更: GateState.enterScanning
2025-08-05 17:58:46.233894: 闸机认证服务启动成功
2025-08-05 17:58:46.234883: subThread :ReaderCommand.readerList
2025-08-05 17:58:46.234883: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:46.234883: readerList：3,readerSetting：3
2025-08-05 17:58:46.234883: cacheUsedReaders:3
2025-08-05 17:58:46.234883: subThread :ReaderCommand.open
2025-08-05 17:58:46.235878: commandRsp:ReaderCommand.open
2025-08-05 17:58:46.402357: dc_init:0xb4 100
2025-08-05 17:58:46.403253: open reader readerType ：10 ret：0
2025-08-05 17:58:46.403253: open reader readerType ：13 ret：0
2025-08-05 17:58:46.407246: widget.port.isOpen:false
2025-08-05 17:58:46.408247: 打开COM4读写失败，SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0
2025-08-05 17:58:46.408247: open reader readerType ：12 ret：-1
2025-08-05 17:58:46.408247: dc_exit:0
2025-08-05 17:58:46.409241: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:46.409241: HD100SS close result: -1
2025-08-05 17:58:46.409241: [[10, 0], [13, 0], [12, -1]]
2025-08-05 17:58:46.409241: changeType:ReaderErrorType.openFail
2025-08-05 17:58:46.410238: 读卡器状态变化: ReaderErrorType.openFail
2025-08-05 17:58:46.410238: 读卡器连接失败，尝试重新连接
2025-08-05 17:58:46.410238: 处理读卡器连接错误
2025-08-05 17:58:46.411257: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:46.411257: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:46.909937: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:47.557079: iReadCardBas ret:4294967294
2025-08-05 17:58:47.560098: 无卡
2025-08-05 17:58:47.560098: 无卡
2025-08-05 17:58:47.561086: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:48.207280: iReadCardBas ret:4294967294
2025-08-05 17:58:48.208172: 无卡
2025-08-05 17:58:48.208172: 无卡
2025-08-05 17:58:48.208172: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:48.411103: 尝试重新配置读卡器
2025-08-05 17:58:48.411103: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:48.411103: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:48.411103: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:48.411103: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:48.412099: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:48.412099: 添加有效设备: type=10, id=10
2025-08-05 17:58:48.412099: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:48.412099: 添加有效设备: type=13, id=13
2025-08-05 17:58:48.412099: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:48.412099: 添加有效设备: type=12, id=12
2025-08-05 17:58:48.412099: 总共加载了3个设备配置
2025-08-05 17:58:48.412099: stopInventory newPort:SendPort
2025-08-05 17:58:48.514511: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:58:48.514511: 读卡器连接已完全关闭
2025-08-05 17:58:48.514511: changeReaders
2025-08-05 17:58:48.515325: createIsolate isOpen:true,isOpening:false
2025-08-05 17:58:48.515325: open():SendPort
2025-08-05 17:58:48.515325: untilDetcted():SendPort
2025-08-05 17:58:48.515325: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:48.855144: iReadCardBas ret:4294967294
2025-08-05 17:58:48.855144: 无卡
2025-08-05 17:58:48.855144: 无卡
2025-08-05 17:58:48.856143: subThread :ReaderCommand.stopInventory
2025-08-05 17:58:48.856143: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:58:48.856143: subThread :ReaderCommand.close
2025-08-05 17:58:48.856143: commandRsp:ReaderCommand.close
2025-08-05 17:58:48.857127: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:58:48.857127: close:T10Bridge
2025-08-05 17:58:48.857127: close:HD100SSBridge
2025-08-05 17:58:48.857127: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:48.857127: HD100SS close result: -1
2025-08-05 17:58:48.857127: close:ScanerBridge
2025-08-05 17:58:48.857127: close:done
2025-08-05 17:58:48.858123: changeType:ReaderErrorType.closeSuccess
2025-08-05 17:58:48.858123: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-05 17:58:48.858123: already close all reader
2025-08-05 17:58:48.858123: subThread :ReaderCommand.readerList
2025-08-05 17:58:48.858123: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:48.858123: readerList：3,readerSetting：3
2025-08-05 17:58:48.859123: cacheUsedReaders:3
2025-08-05 17:58:48.859123: subThread :ReaderCommand.open
2025-08-05 17:58:48.859123: commandRsp:ReaderCommand.open
2025-08-05 17:58:48.867141: dc_init:0xb4 100
2025-08-05 17:58:48.867141: open reader readerType ：10 ret：0
2025-08-05 17:58:48.867141: open reader readerType ：13 ret：0
2025-08-05 17:58:48.873128: widget.port.isOpen:false
2025-08-05 17:58:48.873128: 打开COM4读写失败，SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0
2025-08-05 17:58:48.874125: open reader readerType ：12 ret：-1
2025-08-05 17:58:48.874125: dc_exit:0
2025-08-05 17:58:48.874125: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:48.874125: HD100SS close result: -1
2025-08-05 17:58:48.875127: [[10, 0], [13, 0], [12, -1]]
2025-08-05 17:58:48.875127: changeType:ReaderErrorType.openFail
2025-08-05 17:58:48.875127: 读卡器状态变化: ReaderErrorType.openFail
2025-08-05 17:58:48.875127: 读卡器连接失败，尝试重新连接
2025-08-05 17:58:48.875127: 处理读卡器连接错误
2025-08-05 17:58:48.876119: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:48.876119: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:49.375136: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:50.022301: iReadCardBas ret:4294967294
2025-08-05 17:58:50.022301: 无卡
2025-08-05 17:58:50.022301: 无卡
2025-08-05 17:58:50.022301: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:50.668856: iReadCardBas ret:4294967294
2025-08-05 17:58:50.668856: 无卡
2025-08-05 17:58:50.668856: 无卡
2025-08-05 17:58:50.669845: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:50.876684: 尝试重新配置读卡器
2025-08-05 17:58:50.877500: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:50.877500: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:50.877500: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:50.877500: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:50.877500: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:50.877500: 添加有效设备: type=10, id=10
2025-08-05 17:58:50.877500: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:50.877500: 添加有效设备: type=13, id=13
2025-08-05 17:58:50.877500: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:50.877500: 添加有效设备: type=12, id=12
2025-08-05 17:58:50.877500: 总共加载了3个设备配置
2025-08-05 17:58:50.878517: stopInventory newPort:SendPort
2025-08-05 17:58:50.980771: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:58:50.980771: 读卡器连接已完全关闭
2025-08-05 17:58:50.981628: changeReaders
2025-08-05 17:58:50.981628: createIsolate isOpen:true,isOpening:false
2025-08-05 17:58:50.981628: open():SendPort
2025-08-05 17:58:50.981628: untilDetcted():SendPort
2025-08-05 17:58:50.981628: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:51.316024: iReadCardBas ret:4294967294
2025-08-05 17:58:51.316024: 无卡
2025-08-05 17:58:51.316024: 无卡
2025-08-05 17:58:51.316974: subThread :ReaderCommand.stopInventory
2025-08-05 17:58:51.316974: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:58:51.316974: subThread :ReaderCommand.close
2025-08-05 17:58:51.316974: commandRsp:ReaderCommand.close
2025-08-05 17:58:51.316974: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:58:51.316974: close:T10Bridge
2025-08-05 17:58:51.316974: close:HD100SSBridge
2025-08-05 17:58:51.317971: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:51.317971: HD100SS close result: -1
2025-08-05 17:58:51.317971: close:ScanerBridge
2025-08-05 17:58:51.317971: close:done
2025-08-05 17:58:51.317971: changeType:ReaderErrorType.closeSuccess
2025-08-05 17:58:51.317971: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-05 17:58:51.317971: already close all reader
2025-08-05 17:58:51.317971: subThread :ReaderCommand.readerList
2025-08-05 17:58:51.318968: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:51.318968: readerList：3,readerSetting：3
2025-08-05 17:58:51.318968: cacheUsedReaders:3
2025-08-05 17:58:51.318968: subThread :ReaderCommand.open
2025-08-05 17:58:51.318968: commandRsp:ReaderCommand.open
2025-08-05 17:58:51.322956: dc_init:0xb4 100
2025-08-05 17:58:51.322956: open reader readerType ：10 ret：0
2025-08-05 17:58:51.322956: open reader readerType ：13 ret：0
2025-08-05 17:58:51.327944: widget.port.isOpen:false
2025-08-05 17:58:51.327944: 打开COM4读写失败，SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0
2025-08-05 17:58:51.327944: open reader readerType ：12 ret：-1
2025-08-05 17:58:51.327944: dc_exit:0
2025-08-05 17:58:51.328941: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:51.328941: HD100SS close result: -1
2025-08-05 17:58:51.328941: [[10, 0], [13, 0], [12, -1]]
2025-08-05 17:58:51.328941: changeType:ReaderErrorType.openFail
2025-08-05 17:58:51.329938: 读卡器状态变化: ReaderErrorType.openFail
2025-08-05 17:58:51.329938: 读卡器连接失败，尝试重新连接
2025-08-05 17:58:51.329938: 处理读卡器连接错误
2025-08-05 17:58:51.330935: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:51.330935: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:51.830043: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:52.475976: iReadCardBas ret:4294967294
2025-08-05 17:58:52.475976: 无卡
2025-08-05 17:58:52.476946: 无卡
2025-08-05 17:58:52.476946: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:53.122676: iReadCardBas ret:4294967294
2025-08-05 17:58:53.122676: 无卡
2025-08-05 17:58:53.122676: 无卡
2025-08-05 17:58:53.122676: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:53.331308: 尝试重新配置读卡器
2025-08-05 17:58:53.331308: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:53.332310: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:53.332310: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:53.332310: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:53.332310: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:53.332310: 添加有效设备: type=10, id=10
2025-08-05 17:58:53.332310: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:53.332310: 添加有效设备: type=13, id=13
2025-08-05 17:58:53.332310: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:53.332310: 添加有效设备: type=12, id=12
2025-08-05 17:58:53.332310: 总共加载了3个设备配置
2025-08-05 17:58:53.332310: stopInventory newPort:SendPort
2025-08-05 17:58:53.434241: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:58:53.434241: 读卡器连接已完全关闭
2025-08-05 17:58:53.434241: changeReaders
2025-08-05 17:58:53.434241: createIsolate isOpen:true,isOpening:false
2025-08-05 17:58:53.435219: open():SendPort
2025-08-05 17:58:53.435219: untilDetcted():SendPort
2025-08-05 17:58:53.435219: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:53.769381: iReadCardBas ret:4294967294
2025-08-05 17:58:53.769381: 无卡
2025-08-05 17:58:53.770381: 无卡
2025-08-05 17:58:53.770381: subThread :ReaderCommand.stopInventory
2025-08-05 17:58:53.770381: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:58:53.770381: subThread :ReaderCommand.close
2025-08-05 17:58:53.770381: commandRsp:ReaderCommand.close
2025-08-05 17:58:53.770381: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:58:53.770381: close:T10Bridge
2025-08-05 17:58:53.771378: close:HD100SSBridge
2025-08-05 17:58:53.771378: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:53.771378: HD100SS close result: -1
2025-08-05 17:58:53.771378: close:ScanerBridge
2025-08-05 17:58:53.771378: close:done
2025-08-05 17:58:53.772376: changeType:ReaderErrorType.closeSuccess
2025-08-05 17:58:53.772376: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-05 17:58:53.772376: already close all reader
2025-08-05 17:58:53.772376: subThread :ReaderCommand.readerList
2025-08-05 17:58:53.772376: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:53.773373: readerList：3,readerSetting：3
2025-08-05 17:58:53.773373: cacheUsedReaders:3
2025-08-05 17:58:53.773853: subThread :ReaderCommand.open
2025-08-05 17:58:53.773853: commandRsp:ReaderCommand.open
2025-08-05 17:58:53.778938: dc_init:0xb4 100
2025-08-05 17:58:53.778938: open reader readerType ：10 ret：0
2025-08-05 17:58:53.778938: open reader readerType ：13 ret：0
2025-08-05 17:58:53.783715: widget.port.isOpen:false
2025-08-05 17:58:53.784684: 打开COM4读写失败，SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0
2025-08-05 17:58:53.784684: open reader readerType ：12 ret：-1
2025-08-05 17:58:53.784684: dc_exit:0
2025-08-05 17:58:53.784684: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:53.784684: HD100SS close result: -1
2025-08-05 17:58:53.785681: [[10, 0], [13, 0], [12, -1]]
2025-08-05 17:58:53.785681: changeType:ReaderErrorType.openFail
2025-08-05 17:58:53.785681: 读卡器状态变化: ReaderErrorType.openFail
2025-08-05 17:58:53.785681: 读卡器连接失败，尝试重新连接
2025-08-05 17:58:53.785681: 处理读卡器连接错误
2025-08-05 17:58:53.786678: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:53.786678: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:54.287086: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:54.933725: iReadCardBas ret:4294967294
2025-08-05 17:58:54.934721: 无卡
2025-08-05 17:58:54.934721: 无卡
2025-08-05 17:58:54.934721: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:55.580646: iReadCardBas ret:4294967294
2025-08-05 17:58:55.580646: 无卡
2025-08-05 17:58:55.580646: 无卡
2025-08-05 17:58:55.580646: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:55.787960: 尝试重新配置读卡器
2025-08-05 17:58:55.787960: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:55.788733: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:55.788733: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:55.788733: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:55.788733: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:55.789242: 添加有效设备: type=10, id=10
2025-08-05 17:58:55.789242: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:55.789242: 添加有效设备: type=13, id=13
2025-08-05 17:58:55.789242: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:55.789242: 添加有效设备: type=12, id=12
2025-08-05 17:58:55.789242: 总共加载了3个设备配置
2025-08-05 17:58:55.789242: stopInventory newPort:SendPort
2025-08-05 17:58:55.890841: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:58:55.890841: 读卡器连接已完全关闭
2025-08-05 17:58:55.890841: changeReaders
2025-08-05 17:58:55.891849: createIsolate isOpen:true,isOpening:false
2025-08-05 17:58:55.891849: open():SendPort
2025-08-05 17:58:55.891849: untilDetcted():SendPort
2025-08-05 17:58:55.891849: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:56.226595: iReadCardBas ret:4294967294
2025-08-05 17:58:56.226595: 无卡
2025-08-05 17:58:56.226595: 无卡
2025-08-05 17:58:56.227586: subThread :ReaderCommand.stopInventory
2025-08-05 17:58:56.227586: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:58:56.227586: subThread :ReaderCommand.close
2025-08-05 17:58:56.228575: commandRsp:ReaderCommand.close
2025-08-05 17:58:56.228575: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:58:56.228575: close:T10Bridge
2025-08-05 17:58:56.228575: close:HD100SSBridge
2025-08-05 17:58:56.228575: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:56.229567: HD100SS close result: -1
2025-08-05 17:58:56.229567: close:ScanerBridge
2025-08-05 17:58:56.229567: close:done
2025-08-05 17:58:56.229567: changeType:ReaderErrorType.closeSuccess
2025-08-05 17:58:56.229567: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-05 17:58:56.230562: already close all reader
2025-08-05 17:58:56.230562: subThread :ReaderCommand.readerList
2025-08-05 17:58:56.230562: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:56.230562: readerList：3,readerSetting：3
2025-08-05 17:58:56.230562: cacheUsedReaders:3
2025-08-05 17:58:56.231560: subThread :ReaderCommand.open
2025-08-05 17:58:56.231560: commandRsp:ReaderCommand.open
2025-08-05 17:58:56.234862: dc_init:0xb4 100
2025-08-05 17:58:56.234862: open reader readerType ：10 ret：0
2025-08-05 17:58:56.234862: open reader readerType ：13 ret：0
2025-08-05 17:58:56.239800: widget.port.isOpen:false
2025-08-05 17:58:56.239800: 打开COM4读写失败，SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0
2025-08-05 17:58:56.240774: open reader readerType ：12 ret：-1
2025-08-05 17:58:56.240774: dc_exit:0
2025-08-05 17:58:56.240774: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:56.240774: HD100SS close result: -1
2025-08-05 17:58:56.240774: [[10, 0], [13, 0], [12, -1]]
2025-08-05 17:58:56.241772: changeType:ReaderErrorType.openFail
2025-08-05 17:58:56.241772: 读卡器状态变化: ReaderErrorType.openFail
2025-08-05 17:58:56.241772: 读卡器连接失败，尝试重新连接
2025-08-05 17:58:56.241772: 处理读卡器连接错误
2025-08-05 17:58:56.242769: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:56.242769: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:56.741967: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:57.388324: iReadCardBas ret:4294967294
2025-08-05 17:58:57.388324: 无卡
2025-08-05 17:58:57.388324: 无卡
2025-08-05 17:58:57.389322: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:58.034759: iReadCardBas ret:4294967294
2025-08-05 17:58:58.034759: 无卡
2025-08-05 17:58:58.034759: 无卡
2025-08-05 17:58:58.034759: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:58.243503: 尝试重新配置读卡器
2025-08-05 17:58:58.243503: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:58:58.244502: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:58:58.244502: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:58:58.244502: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:58:58.244502: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:58:58.244502: 添加有效设备: type=10, id=10
2025-08-05 17:58:58.244502: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:58:58.244502: 添加有效设备: type=13, id=13
2025-08-05 17:58:58.244502: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:58:58.244502: 添加有效设备: type=12, id=12
2025-08-05 17:58:58.244502: 总共加载了3个设备配置
2025-08-05 17:58:58.244502: stopInventory newPort:SendPort
2025-08-05 17:58:58.347277: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:58:58.347277: 读卡器连接已完全关闭
2025-08-05 17:58:58.348126: changeReaders
2025-08-05 17:58:58.348126: createIsolate isOpen:true,isOpening:false
2025-08-05 17:58:58.348126: open():SendPort
2025-08-05 17:58:58.348126: untilDetcted():SendPort
2025-08-05 17:58:58.349079: 读卡器配置完成，共 3 个设备
2025-08-05 17:58:58.680862: iReadCardBas ret:4294967294
2025-08-05 17:58:58.680862: 无卡
2025-08-05 17:58:58.680862: 无卡
2025-08-05 17:58:58.680862: subThread :ReaderCommand.stopInventory
2025-08-05 17:58:58.681860: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:58:58.681860: subThread :ReaderCommand.close
2025-08-05 17:58:58.681860: commandRsp:ReaderCommand.close
2025-08-05 17:58:58.681860: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:58:58.681860: close:T10Bridge
2025-08-05 17:58:58.681860: close:HD100SSBridge
2025-08-05 17:58:58.682856: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:58:58.682856: HD100SS close result: -1
2025-08-05 17:58:58.682856: close:ScanerBridge
2025-08-05 17:58:58.682856: close:done
2025-08-05 17:58:58.682856: changeType:ReaderErrorType.closeSuccess
2025-08-05 17:58:58.682856: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-05 17:58:58.682856: already close all reader
2025-08-05 17:58:58.683854: subThread :ReaderCommand.readerList
2025-08-05 17:58:58.683854: commandRsp:ReaderCommand.readerList
2025-08-05 17:58:58.683854: readerList：3,readerSetting：3
2025-08-05 17:58:58.683854: cacheUsedReaders:3
2025-08-05 17:58:58.683854: subThread :ReaderCommand.open
2025-08-05 17:58:58.683854: commandRsp:ReaderCommand.open
2025-08-05 17:58:58.690836: dc_init:0xb4 100
2025-08-05 17:58:58.690836: open reader readerType ：10 ret：0
2025-08-05 17:58:58.690836: open reader readerType ：13 ret：0
2025-08-05 17:58:58.698813: 去开启 读 监听
2025-08-05 17:58:58.698813: widget.port.isOpen:true
2025-08-05 17:58:58.704866: 打开COM4读写成功
2025-08-05 17:58:58.704866: 发送：[16, 54, 0d]
2025-08-05 17:58:58.704866: open reader readerType ：12 ret：0
2025-08-05 17:58:58.705868: [[10, 0], [13, 0], [12, 0]]
2025-08-05 17:58:58.705868: changeType:ReaderErrorType.openSuccess
2025-08-05 17:58:58.705868: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-05 17:58:58.705868: 读卡器连接成功
2025-08-05 17:58:58.705868: 读卡器连接成功，确保扫描状态正常
2025-08-05 17:58:58.705868: 恢复读卡器扫描状态...
2025-08-05 17:58:58.706863: 读卡器扫描状态已恢复
2025-08-05 17:58:58.706863: subThread :ReaderCommand.untilDetected
2025-08-05 17:58:58.707861: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:58:58.707861: subThread :ReaderCommand.resumeInventory
2025-08-05 17:58:58.707861: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:58:59.211033: dc_config_card:0
2025-08-05 17:58:59.227096: dc_card_n_hex:1,len:0
2025-08-05 17:58:59.227096: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:58:59.874394: iReadCardBas ret:4294967294
2025-08-05 17:58:59.874394: 无卡
2025-08-05 17:58:59.874394: 无卡
2025-08-05 17:58:59.882892: dc_config_card:0
2025-08-05 17:58:59.898834: dc_card_n_hex:1,len:0
2025-08-05 17:58:59.898834: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:00.545411: iReadCardBas ret:4294967294
2025-08-05 17:59:00.546426: 无卡
2025-08-05 17:59:00.546426: 无卡
2025-08-05 17:59:00.554947: dc_config_card:0
2025-08-05 17:59:00.570949: dc_card_n_hex:1,len:0
2025-08-05 17:59:00.570949: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:01.217068: iReadCardBas ret:4294967294
2025-08-05 17:59:01.218083: 无卡
2025-08-05 17:59:01.218083: 无卡
2025-08-05 17:59:01.218083: dc_config_card:0
2025-08-05 17:59:01.234300: dc_card_n_hex:1,len:0
2025-08-05 17:59:01.234300: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:01.881456: iReadCardBas ret:4294967294
2025-08-05 17:59:01.881456: 无卡
2025-08-05 17:59:01.881456: 无卡
2025-08-05 17:59:01.882455: 收到：[57, 48, 48, 48, 53, 50, 13]
2025-08-05 17:59:01.882455: dc_config_card:0
2025-08-05 17:59:01.898994: dc_card_n_hex:1,len:0
2025-08-05 17:59:01.898994: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:02.545061: iReadCardBas ret:4294967294
2025-08-05 17:59:02.546063: 无卡
2025-08-05 17:59:02.546063: 无卡
2025-08-05 17:59:02.554780: dc_config_card:0
2025-08-05 17:59:02.571015: dc_card_n_hex:1,len:0
2025-08-05 17:59:02.571533: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:03.217655: iReadCardBas ret:4294967294
2025-08-05 17:59:03.218679: 无卡
2025-08-05 17:59:03.218679: 无卡
2025-08-05 17:59:03.226967: dc_config_card:0
2025-08-05 17:59:03.242957: dc_card_n_hex:1,len:0
2025-08-05 17:59:03.242957: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:03.890955: iReadCardBas ret:4294967294
2025-08-05 17:59:03.890955: 无卡
2025-08-05 17:59:03.890955: 无卡
2025-08-05 17:59:03.899143: dc_config_card:0
2025-08-05 17:59:03.914992: dc_card_n_hex:1,len:0
2025-08-05 17:59:03.914992: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:04.561522: iReadCardBas ret:4294967294
2025-08-05 17:59:04.561522: 无卡
2025-08-05 17:59:04.562507: 无卡
2025-08-05 17:59:04.562507: catch exception:SerialPortError: ²Ù×÷³É¹¦Íê³É¡£, errno = 0,
  stack:
2025-08-05 17:59:04.570189: dc_config_card:0
2025-08-05 17:59:04.586589: dc_card_n_hex:1,len:0
2025-08-05 17:59:04.586589: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:05.233031: iReadCardBas ret:4294967294
2025-08-05 17:59:05.233031: 无卡
2025-08-05 17:59:05.233031: 无卡
2025-08-05 17:59:05.233994: dc_config_card:0
2025-08-05 17:59:05.250157: dc_card_n_hex:1,len:0
2025-08-05 17:59:05.251173: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:05.896384: iReadCardBas ret:4294967294
2025-08-05 17:59:05.896384: 无卡
2025-08-05 17:59:05.896384: 无卡
2025-08-05 17:59:05.898241: dc_config_card:0
2025-08-05 17:59:05.915028: dc_card_n_hex:1,len:0
2025-08-05 17:59:05.915028: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:06.560320: iReadCardBas ret:4294967294
2025-08-05 17:59:06.561317: 无卡
2025-08-05 17:59:06.561317: 无卡
2025-08-05 17:59:06.562314: dc_config_card:0
2025-08-05 17:59:06.578554: dc_card_n_hex:1,len:0
2025-08-05 17:59:06.578554: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:07.225056: iReadCardBas ret:4294967294
2025-08-05 17:59:07.225056: 无卡
2025-08-05 17:59:07.225056: 无卡
2025-08-05 17:59:07.227037: dc_config_card:0
2025-08-05 17:59:07.242936: dc_card_n_hex:1,len:0
2025-08-05 17:59:07.242936: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:07.890282: iReadCardBas ret:4294967294
2025-08-05 17:59:07.890282: 无卡
2025-08-05 17:59:07.890282: 无卡
2025-08-05 17:59:07.898911: dc_config_card:0
2025-08-05 17:59:07.915152: dc_card_n_hex:1,len:0
2025-08-05 17:59:07.915152: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:08.561289: iReadCardBas ret:4294967294
2025-08-05 17:59:08.562070: 无卡
2025-08-05 17:59:08.562070: 无卡
2025-08-05 17:59:08.570546: dc_config_card:0
2025-08-05 17:59:08.586604: dc_card_n_hex:1,len:0
2025-08-05 17:59:08.586604: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:09.233572: iReadCardBas ret:4294967294
2025-08-05 17:59:09.233572: 无卡
2025-08-05 17:59:09.234574: 无卡
2025-08-05 17:59:09.242737: dc_config_card:0
2025-08-05 17:59:09.258883: dc_card_n_hex:1,len:0
2025-08-05 17:59:09.258883: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:09.904708: iReadCardBas ret:4294967294
2025-08-05 17:59:09.905705: 无卡
2025-08-05 17:59:09.905705: 无卡
2025-08-05 17:59:09.906702: dc_config_card:0
2025-08-05 17:59:09.922691: dc_card_n_hex:1,len:0
2025-08-05 17:59:09.922691: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:10.568978: iReadCardBas ret:4294967294
2025-08-05 17:59:10.568978: 无卡
2025-08-05 17:59:10.569980: 无卡
2025-08-05 17:59:10.571016: dc_config_card:0
2025-08-05 17:59:10.586252: dc_card_n_hex:1,len:0
2025-08-05 17:59:10.587240: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:11.232979: iReadCardBas ret:4294967294
2025-08-05 17:59:11.233979: 无卡
2025-08-05 17:59:11.233979: 无卡
2025-08-05 17:59:11.243042: dc_config_card:0
2025-08-05 17:59:11.258987: dc_card_n_hex:1,len:0
2025-08-05 17:59:11.258987: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:11.905788: iReadCardBas ret:4294967294
2025-08-05 17:59:11.906802: 无卡
2025-08-05 17:59:11.906802: 无卡
2025-08-05 17:59:11.914945: dc_config_card:0
2025-08-05 17:59:11.930604: dc_card_n_hex:1,len:0
2025-08-05 17:59:11.930604: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:12.576516: iReadCardBas ret:4294967294
2025-08-05 17:59:12.577514: 无卡
2025-08-05 17:59:12.577514: 无卡
2025-08-05 17:59:12.578511: dc_config_card:0
2025-08-05 17:59:12.594871: dc_card_n_hex:1,len:0
2025-08-05 17:59:12.594871: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:13.241812: iReadCardBas ret:4294967294
2025-08-05 17:59:13.241812: 无卡
2025-08-05 17:59:13.241812: 无卡
2025-08-05 17:59:13.250878: dc_config_card:0
2025-08-05 17:59:13.267011: dc_card_n_hex:1,len:0
2025-08-05 17:59:13.267011: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:13.913841: iReadCardBas ret:4294967294
2025-08-05 17:59:13.913841: 无卡
2025-08-05 17:59:13.913841: 无卡
2025-08-05 17:59:13.922542: dc_config_card:0
2025-08-05 17:59:13.938728: dc_card_n_hex:1,len:0
2025-08-05 17:59:13.938728: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:14.584941: iReadCardBas ret:4294967294
2025-08-05 17:59:14.584941: 无卡
2025-08-05 17:59:14.584941: 无卡
2025-08-05 17:59:14.586929: dc_config_card:0
2025-08-05 17:59:14.602750: dc_card_n_hex:1,len:0
2025-08-05 17:59:14.602750: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:15.249055: iReadCardBas ret:4294967294
2025-08-05 17:59:15.250055: 无卡
2025-08-05 17:59:15.250055: 无卡
2025-08-05 17:59:15.259031: dc_config_card:0
2025-08-05 17:59:15.274525: dc_card_n_hex:1,len:0
2025-08-05 17:59:15.275511: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:15.922436: iReadCardBas ret:4294967294
2025-08-05 17:59:15.922436: 无卡
2025-08-05 17:59:15.922436: 无卡
2025-08-05 17:59:15.930525: dc_config_card:0
2025-08-05 17:59:15.946501: dc_card_n_hex:1,len:0
2025-08-05 17:59:15.946501: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:16.234348: 闸机认证超时
2025-08-05 17:59:16.234348: 收到认证结果: AuthStatus.failureTimeout, 用户: null
2025-08-05 17:59:16.234348: 收到闸机认证结果: AuthStatus.failureTimeout, 用户: null
2025-08-05 17:59:16.234348: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:16.539482: 收到串口命令: enter_end (进馆结束)
2025-08-05 17:59:16.539482: 处理进馆结束
2025-08-05 17:59:16.540479: 停止闸机认证服务...
2025-08-05 17:59:16.540479: 停止所有认证方式监听
2025-08-05 17:59:16.540479: 停止微信扫码认证监听
2025-08-05 17:59:16.540479: 停止二维码扫描认证监听
2025-08-05 17:59:16.540479: 停止读者证认证监听
2025-08-05 17:59:16.541476: 停止读卡器认证监听
2025-08-05 17:59:16.541476: 停止社保卡认证监听
2025-08-05 17:59:16.541476: 停止读卡器认证监听
2025-08-05 17:59:16.541476: 停止电子社保卡认证监听
2025-08-05 17:59:16.542474: 停止读卡器认证监听
2025-08-05 17:59:16.542474: 二维码扫描认证监听已停止
2025-08-05 17:59:16.542474: 微信扫码认证服务监听已停止
2025-08-05 17:59:16.542474: 已移除读卡器状态监听器
2025-08-05 17:59:16.542474: 已移除标签数据监听器
2025-08-05 17:59:16.542474: 所有卡片监听器已移除
2025-08-05 17:59:16.542474: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:16.542474: stopInventory newPort:SendPort
2025-08-05 17:59:16.543470: 已移除读卡器状态监听器
2025-08-05 17:59:16.543470: 已移除标签数据监听器
2025-08-05 17:59:16.543470: 所有卡片监听器已移除
2025-08-05 17:59:16.543470: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:16.543470: stopInventory newPort:SendPort
2025-08-05 17:59:16.543470: 已移除读卡器状态监听器
2025-08-05 17:59:16.543470: 已移除标签数据监听器
2025-08-05 17:59:16.543470: 所有卡片监听器已移除
2025-08-05 17:59:16.543470: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:16.544467: stopInventory newPort:SendPort
2025-08-05 17:59:16.592559: iReadCardBas ret:4294967294
2025-08-05 17:59:16.592559: 无卡
2025-08-05 17:59:16.593569: 无卡
2025-08-05 17:59:16.593569: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:16.593569: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:16.593569: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:16.594569: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:16.594569: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:16.594569: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:16.644621: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:16.644621: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:16.645435: 读者证认证服务监听已停止
2025-08-05 17:59:16.645435: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:16.645435: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:16.645435: 社保卡认证服务监听已停止
2025-08-05 17:59:16.646642: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:16.647313: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:16.647313: 电子社保卡认证服务监听已停止
2025-08-05 17:59:16.647313: 多认证管理器状态变更: idle
2025-08-05 17:59:16.647313: 所有认证方式监听已停止
2025-08-05 17:59:16.647313: 闸机认证服务已停止
2025-08-05 17:59:16.647313: 闸机状态变更: GateState.enterOver
2025-08-05 17:59:17.648863: 闸机认证服务未在运行
2025-08-05 17:59:17.648863: 闸机状态变更: GateState.idle
2025-08-05 17:59:17.648863: 页面状态变更: SilencePageState.welcome
2025-08-05 17:59:19.083626: 收到串口命令: enter_start (进馆开始)
2025-08-05 17:59:19.084627: 处理进馆开始
2025-08-05 17:59:19.084627: 闸机状态变更: GateState.enterStarted
2025-08-05 17:59:19.084627: 页面状态变更: SilencePageState.authenticating
2025-08-05 17:59:19.084627: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:19.084627: 启动闸机认证服务...
2025-08-05 17:59:19.084627: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:19.084627: 开始启动MultiAuthManager监听...
2025-08-05 17:59:19.084627: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:19.084627: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:19.084627: 多认证管理器状态变更: listening
2025-08-05 17:59:19.085645: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:59:19.085645: 准备启动 2 个物理认证服务
2025-08-05 17:59:19.085645: 开始二维码扫描认证监听
2025-08-05 17:59:19.085645: 开始二维码扫描监听
2025-08-05 17:59:19.085645: 二维码扫描认证监听启动成功
2025-08-05 17:59:19.085645: 微信扫码 认证服务启动成功
2025-08-05 17:59:19.085645: 开始读卡器认证监听
2025-08-05 17:59:19.086622: 强制重新配置读卡器以确保状态一致性
2025-08-05 17:59:19.086622: 完全重置读卡器连接和监听器状态...
2025-08-05 17:59:19.086622: 已移除读卡器状态监听器
2025-08-05 17:59:19.086622: 已移除标签数据监听器
2025-08-05 17:59:19.086622: 所有卡片监听器已移除
2025-08-05 17:59:19.086622: stopInventory newPort:SendPort
2025-08-05 17:59:19.086622: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:19.087646: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:19.287669: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:19.287669: subThread :ReaderCommand.close
2025-08-05 17:59:19.288512: commandRsp:ReaderCommand.close
2025-08-05 17:59:19.288512: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:19.288512: close:T10Bridge
2025-08-05 17:59:19.289509: dc_exit:0
2025-08-05 17:59:19.289509: close:HD100SSBridge
2025-08-05 17:59:19.289509: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:19.290517: HD100SS close result: -1
2025-08-05 17:59:19.290517: close:ScanerBridge
2025-08-05 17:59:19.290517: 发送：[16, 55, 0d]
2025-08-05 17:59:19.290517: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:19.290517: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:19.389644: 读卡器连接已完全关闭
2025-08-05 17:59:19.389644: 读卡器连接和监听器状态已完全重置
2025-08-05 17:59:19.389644: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:59:19.389644: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:59:19.390612: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:59:19.390612: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:59:19.390612: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:59:19.390612: 添加有效设备: type=10, id=10
2025-08-05 17:59:19.390612: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:59:19.390612: 添加有效设备: type=13, id=13
2025-08-05 17:59:19.390612: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:59:19.390612: 添加有效设备: type=12, id=12
2025-08-05 17:59:19.390612: 总共加载了3个设备配置
2025-08-05 17:59:19.390612: stopInventory newPort:SendPort
2025-08-05 17:59:19.391632: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:19.391632: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:19.493763: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:19.493763: 读卡器连接已完全关闭
2025-08-05 17:59:19.493763: changeReaders
2025-08-05 17:59:19.494732: createIsolate isOpen:true,isOpening:false
2025-08-05 17:59:19.494732: open():SendPort
2025-08-05 17:59:19.494732: untilDetcted():SendPort
2025-08-05 17:59:19.495732: 读卡器配置完成，共 3 个设备
2025-08-05 17:59:19.495732: 已移除读卡器状态监听器
2025-08-05 17:59:19.496725: 已移除标签数据监听器
2025-08-05 17:59:19.496725: 所有卡片监听器已移除
2025-08-05 17:59:19.496725: 已添加读卡器状态监听器
2025-08-05 17:59:19.496725: 已添加标签数据监听器
2025-08-05 17:59:19.496725: 开始监听卡片数据 - 所有监听器已就绪
2025-08-05 17:59:19.496725: 读卡器认证监听启动成功
2025-08-05 17:59:19.496725: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-05 17:59:19.496725: 所有认证服务启动完成，成功启动 2 个服务
2025-08-05 17:59:19.496725: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-05 17:59:19.496725: MultiAuthManager启动监听成功
2025-08-05 17:59:19.496725: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-05 17:59:19.496725: 闸机状态变更: GateState.enterScanning
2025-08-05 17:59:19.497722: 闸机认证服务启动成功
2025-08-05 17:59:19.497722: subThread :ReaderCommand.close
2025-08-05 17:59:19.497722: commandRsp:ReaderCommand.close
2025-08-05 17:59:19.497722: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:19.497722: close:T10Bridge
2025-08-05 17:59:19.497722: close:HD100SSBridge
2025-08-05 17:59:19.498731: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:19.498731: HD100SS close result: -1
2025-08-05 17:59:19.498731: close:ScanerBridge
2025-08-05 17:59:19.498731: 发送：[16, 55, 0d]
2025-08-05 17:59:19.498731: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:19.498731: 读卡器状态变化: ReaderErrorType.closeFail
2025-08-05 17:59:19.499717: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:19.499717: subThread :ReaderCommand.readerList
2025-08-05 17:59:19.499717: commandRsp:ReaderCommand.readerList
2025-08-05 17:59:19.499717: readerList：3,readerSetting：3
2025-08-05 17:59:19.499717: cacheUsedReaders:3
2025-08-05 17:59:19.499717: subThread :ReaderCommand.open
2025-08-05 17:59:19.500715: commandRsp:ReaderCommand.open
2025-08-05 17:59:19.595009: dc_init:0xb4 100
2025-08-05 17:59:19.595009: open reader readerType ：10 ret：0
2025-08-05 17:59:19.595009: open reader readerType ：13 ret：0
2025-08-05 17:59:19.596011: open reader readerType ：12 ret：0
2025-08-05 17:59:19.596011: [[10, 0], [13, 0], [12, 0]]
2025-08-05 17:59:19.596011: changeType:ReaderErrorType.openSuccess
2025-08-05 17:59:19.597022: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-05 17:59:19.597022: 读卡器连接成功
2025-08-05 17:59:19.597022: 读卡器连接成功，确保扫描状态正常
2025-08-05 17:59:19.597022: 恢复读卡器扫描状态...
2025-08-05 17:59:19.597022: 读卡器扫描状态已恢复
2025-08-05 17:59:19.598014: subThread :ReaderCommand.untilDetected
2025-08-05 17:59:19.599013: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:59:19.599013: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:19.599013: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:20.099048: dc_config_card:0
2025-08-05 17:59:20.115133: dc_card_n_hex:1,len:0
2025-08-05 17:59:20.115133: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:20.761985: iReadCardBas ret:4294967294
2025-08-05 17:59:20.762934: 无卡
2025-08-05 17:59:20.762934: 无卡
2025-08-05 17:59:20.770425: dc_config_card:0
2025-08-05 17:59:20.786288: dc_card_n_hex:1,len:0
2025-08-05 17:59:20.786288: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:21.433821: iReadCardBas ret:4294967294
2025-08-05 17:59:21.433821: 无卡
2025-08-05 17:59:21.433821: 无卡
2025-08-05 17:59:21.442313: dc_config_card:0
2025-08-05 17:59:21.458902: dc_card_n_hex:1,len:0
2025-08-05 17:59:21.458902: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:22.105685: iReadCardBas ret:4294967294
2025-08-05 17:59:22.106671: 无卡
2025-08-05 17:59:22.106671: 无卡
2025-08-05 17:59:22.114961: dc_config_card:0
2025-08-05 17:59:22.130907: dc_card_n_hex:1,len:0
2025-08-05 17:59:22.130907: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:22.778141: iReadCardBas ret:4294967294
2025-08-05 17:59:22.778849: 无卡
2025-08-05 17:59:22.778849: 无卡
2025-08-05 17:59:22.786831: dc_config_card:0
2025-08-05 17:59:22.802879: dc_card_n_hex:1,len:0
2025-08-05 17:59:22.802879: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:23.450112: iReadCardBas ret:4294967294
2025-08-05 17:59:23.450112: 无卡
2025-08-05 17:59:23.450112: 无卡
2025-08-05 17:59:23.458290: dc_config_card:0
2025-08-05 17:59:23.474937: dc_card_n_hex:1,len:0
2025-08-05 17:59:23.474937: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:24.122171: iReadCardBas ret:4294967294
2025-08-05 17:59:24.123067: 无卡
2025-08-05 17:59:24.123067: 无卡
2025-08-05 17:59:24.130364: dc_config_card:0
2025-08-05 17:59:24.147006: dc_card_n_hex:1,len:0
2025-08-05 17:59:24.147006: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:24.794166: iReadCardBas ret:4294967294
2025-08-05 17:59:24.794166: 无卡
2025-08-05 17:59:24.794166: 无卡
2025-08-05 17:59:24.802395: dc_config_card:0
2025-08-05 17:59:24.818932: dc_card_n_hex:1,len:0
2025-08-05 17:59:24.819368: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:25.465953: iReadCardBas ret:4294967294
2025-08-05 17:59:25.465953: 无卡
2025-08-05 17:59:25.465953: 无卡
2025-08-05 17:59:25.474499: dc_config_card:0
2025-08-05 17:59:25.490962: dc_card_n_hex:1,len:0
2025-08-05 17:59:25.490962: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:26.137796: iReadCardBas ret:4294967294
2025-08-05 17:59:26.137796: 无卡
2025-08-05 17:59:26.137796: 无卡
2025-08-05 17:59:26.146926: dc_config_card:0
2025-08-05 17:59:26.162962: dc_card_n_hex:1,len:0
2025-08-05 17:59:26.162962: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:26.529381: 发送心跳
2025-08-05 17:59:26.529381: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY3AZFC9E
2025-08-05 17:59:26.546671: Rsp : 98YYYNNN00500320250805    1801012.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY3AZD528
2025-08-05 17:59:26.809535: iReadCardBas ret:4294967294
2025-08-05 17:59:26.809535: 无卡
2025-08-05 17:59:26.809535: 无卡
2025-08-05 17:59:26.810560: dc_config_card:0
2025-08-05 17:59:26.826992: dc_card_n_hex:1,len:0
2025-08-05 17:59:26.826992: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:27.474152: iReadCardBas ret:4294967294
2025-08-05 17:59:27.474152: 无卡
2025-08-05 17:59:27.475154: 无卡
2025-08-05 17:59:27.483126: dc_config_card:0
2025-08-05 17:59:27.498687: dc_card_n_hex:1,len:0
2025-08-05 17:59:27.498687: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:28.145359: iReadCardBas ret:4294967294
2025-08-05 17:59:28.146187: 无卡
2025-08-05 17:59:28.146187: 无卡
2025-08-05 17:59:28.154184: dc_config_card:0
2025-08-05 17:59:28.242944: dc_card_n_hex:0,len:4
2025-08-05 17:59:28.242944: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-05 17:59:28.251006: dc_authentication_pass:0
2025-08-05 17:59:28.258743: dc_read:0
2025-08-05 17:59:28.258743: data:67643131313131310000000000000000,coder:Std14443ACoder
2025-08-05 17:59:28.258743: parseRet：{"barCode":"gd111111"},decoder:14443AStd
2025-08-05 17:59:28.259744: 检测到二维码数据: gd111111
2025-08-05 17:59:28.260745: 未知的二维码类型: gd111111
2025-08-05 17:59:28.260745: 检测到二维码数据: gd111111
2025-08-05 17:59:28.260745: 未知的二维码类型: gd111111
2025-08-05 17:59:28.260745: 检测到读卡器数据: 1条
2025-08-05 17:59:28.260745: 读卡器数据认证：
2025-08-05 17:59:28.260745:   设备类型: 10
2025-08-05 17:59:28.260745:   条码: gd111111
2025-08-05 17:59:28.260745:   标签UID: FC98BDE2
2025-08-05 17:59:28.261758:   对应登录类型: AuthLoginType.readerCard
2025-08-05 17:59:28.261758:   根据读卡器类型10确定认证方式为: 读者证
2025-08-05 17:59:28.261758:   开始调用认证API: gd111111
2025-08-05 17:59:28.261758: 正在认证用户: gd111111, 方式: 读者证
2025-08-05 17:59:28.261758: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-05 17:59:28.261758: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:28.261758: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:28.261758: 多认证管理器: 读者证获得认证请求锁
2025-08-05 17:59:28.261758: 63 CardType 值为空
2025-08-05 17:59:28.261758: Req msgType：Sip2MsgType.patronInformation ,length:73， ret:  6300120250805    175928  Y       AOhlsp|AAgd111111|TY|BP1|BQ20|AY4AZEF42
2025-08-05 17:59:28.262756: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:28.395072: Rsp : 64YYYYYYYYYYYYYY00020250805    180103000000000000000000000000AOhlsp|AAgd111111|XO|AEgd111111|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY4AZBFDB
2025-08-05 17:59:28.408775: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250805    180103, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBFDB}
2025-08-05 17:59:28.408775: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250805    180103, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBFDB}
2025-08-05 17:59:28.409582: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
2025-08-05 17:59:28.409582: 认证失败，继续监听新的卡片扫描...
2025-08-05 17:59:28.409582:   认证结果: AuthStatus.failureNoMatch, 方式: 读者证
2025-08-05 17:59:28.409582: 认证失败，继续监听下一个用户
2025-08-05 17:59:28.409582: 恢复读卡器扫描状态...
2025-08-05 17:59:28.410579: 读卡器扫描状态已恢复
2025-08-05 17:59:28.410579: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-05 17:59:28.410579: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:28.410579: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:28.410579: 多认证管理器状态变更: authenticating
2025-08-05 17:59:28.410579: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-05 17:59:28.411587: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:28.411587: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-05 17:59:28.411587: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-05 17:59:28.411587: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:28.411587: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-05 17:59:28.411587: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-05 17:59:28.411587: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:28.412586: 非认证状态收到认证结果，忽略
2025-08-05 17:59:28.412586: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.412586: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.412586: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:28.412586: 非认证状态收到认证结果，忽略
2025-08-05 17:59:28.412586: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.413570: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.413570: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:28.413570: 非认证状态收到认证结果，忽略
2025-08-05 17:59:28.413570: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.413570: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:28.413570: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:28.904885: iReadCardBas ret:4294967294
2025-08-05 17:59:28.905886: 无卡
2025-08-05 17:59:28.905886: 无卡
2025-08-05 17:59:28.905886: 检测到了卡 来暂停 ：1
2025-08-05 17:59:28.905886: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:28.905886: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:28.906882: dc_config_card:0
2025-08-05 17:59:29.002191: dc_card_n_hex:0,len:4
2025-08-05 17:59:29.003207: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-05 17:59:29.010864: dc_authentication_pass:0
2025-08-05 17:59:29.018781: dc_read:0
2025-08-05 17:59:29.019078: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:29.664997: iReadCardBas ret:4294967294
2025-08-05 17:59:29.664997: 无卡
2025-08-05 17:59:29.665999: 无卡
2025-08-05 17:59:29.665999: 检测到了卡 来暂停 ：1
2025-08-05 17:59:29.665999: 检测到二维码数据: gd111111
2025-08-05 17:59:29.665999: 未知的二维码类型: gd111111
2025-08-05 17:59:29.666996: 检测到二维码数据: gd111111
2025-08-05 17:59:29.666996: 未知的二维码类型: gd111111
2025-08-05 17:59:31.411913: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-05 17:59:31.411913: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:31.412736: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:31.412736: 多认证管理器状态变更: listening
2025-08-05 17:59:33.411121: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-05 17:59:33.411121: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-05 17:59:33.411121: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:33.411121: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:33.416311: 停止闸机认证服务...
2025-08-05 17:59:33.416311: 闸机状态变更: GateState.idle
2025-08-05 17:59:33.416311: 页面状态变更: SilencePageState.welcome
2025-08-05 17:59:33.417322: 停止所有认证方式监听
2025-08-05 17:59:33.417322: 停止微信扫码认证监听
2025-08-05 17:59:33.417322: 停止二维码扫描认证监听
2025-08-05 17:59:33.417322: 停止读者证认证监听
2025-08-05 17:59:33.417322: 停止读卡器认证监听
2025-08-05 17:59:33.417322: 停止社保卡认证监听
2025-08-05 17:59:33.418307: 停止读卡器认证监听
2025-08-05 17:59:33.418307: 停止电子社保卡认证监听
2025-08-05 17:59:33.418307: 停止读卡器认证监听
2025-08-05 17:59:33.418307: 二维码扫描认证监听已停止
2025-08-05 17:59:33.419305: 微信扫码认证服务监听已停止
2025-08-05 17:59:33.419305: 已移除读卡器状态监听器
2025-08-05 17:59:33.419305: 已移除标签数据监听器
2025-08-05 17:59:33.419305: 所有卡片监听器已移除
2025-08-05 17:59:33.419305: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:33.419305: stopInventory newPort:SendPort
2025-08-05 17:59:33.419305: 已移除读卡器状态监听器
2025-08-05 17:59:33.419305: 已移除标签数据监听器
2025-08-05 17:59:33.419305: 所有卡片监听器已移除
2025-08-05 17:59:33.419305: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:33.420331: stopInventory newPort:SendPort
2025-08-05 17:59:33.420331: 已移除读卡器状态监听器
2025-08-05 17:59:33.420331: 已移除标签数据监听器
2025-08-05 17:59:33.420331: 所有卡片监听器已移除
2025-08-05 17:59:33.420331: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:33.420331: stopInventory newPort:SendPort
2025-08-05 17:59:33.420331: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:33.420331: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:33.421305: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:33.421305: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:33.421305: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:33.421305: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:33.521946: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:33.522767: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:33.522767: 读者证认证服务监听已停止
2025-08-05 17:59:33.522767: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:33.522767: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:33.523762: 社保卡认证服务监听已停止
2025-08-05 17:59:33.523762: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:33.523762: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:33.523762: 电子社保卡认证服务监听已停止
2025-08-05 17:59:33.523762: 多认证管理器状态变更: idle
2025-08-05 17:59:33.523762: 所有认证方式监听已停止
2025-08-05 17:59:33.523762: 闸机认证服务已停止
2025-08-05 17:59:37.963381: 收到串口命令: enter_start (进馆开始)
2025-08-05 17:59:37.964387: 处理进馆开始
2025-08-05 17:59:37.964387: 闸机状态变更: GateState.enterStarted
2025-08-05 17:59:37.964387: 页面状态变更: SilencePageState.authenticating
2025-08-05 17:59:37.964387: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:37.964387: 启动闸机认证服务...
2025-08-05 17:59:37.964387: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:37.964387: 开始启动MultiAuthManager监听...
2025-08-05 17:59:37.965395: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:37.965395: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:37.965395: 多认证管理器状态变更: listening
2025-08-05 17:59:37.965395: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:59:37.965395: 准备启动 2 个物理认证服务
2025-08-05 17:59:37.965395: 开始二维码扫描认证监听
2025-08-05 17:59:37.965395: 开始二维码扫描监听
2025-08-05 17:59:37.965395: 二维码扫描认证监听启动成功
2025-08-05 17:59:37.965395: 微信扫码 认证服务启动成功
2025-08-05 17:59:37.966372: 开始读卡器认证监听
2025-08-05 17:59:37.966372: 强制重新配置读卡器以确保状态一致性
2025-08-05 17:59:37.966372: 完全重置读卡器连接和监听器状态...
2025-08-05 17:59:37.966372: 已移除读卡器状态监听器
2025-08-05 17:59:37.966372: 已移除标签数据监听器
2025-08-05 17:59:37.967389: 所有卡片监听器已移除
2025-08-05 17:59:37.967389: stopInventory newPort:SendPort
2025-08-05 17:59:37.967389: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:37.967389: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:38.169352: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:38.169352: subThread :ReaderCommand.close
2025-08-05 17:59:38.170163: commandRsp:ReaderCommand.close
2025-08-05 17:59:38.170163: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:38.170163: close:T10Bridge
2025-08-05 17:59:38.170163: dc_exit:0
2025-08-05 17:59:38.170163: close:HD100SSBridge
2025-08-05 17:59:38.171197: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:38.171197: HD100SS close result: -1
2025-08-05 17:59:38.171197: close:ScanerBridge
2025-08-05 17:59:38.171197: 发送：[16, 55, 0d]
2025-08-05 17:59:38.171197: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:38.172155: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:38.271460: 读卡器连接已完全关闭
2025-08-05 17:59:38.272458: 读卡器连接和监听器状态已完全重置
2025-08-05 17:59:38.272458: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:59:38.272458: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:59:38.272458: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:59:38.272458: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:59:38.272458: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:59:38.273456: 添加有效设备: type=10, id=10
2025-08-05 17:59:38.273456: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:59:38.273456: 添加有效设备: type=13, id=13
2025-08-05 17:59:38.273456: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:59:38.273456: 添加有效设备: type=12, id=12
2025-08-05 17:59:38.273456: 总共加载了3个设备配置
2025-08-05 17:59:38.273456: stopInventory newPort:SendPort
2025-08-05 17:59:38.274452: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:38.274452: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:38.374506: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:38.374506: 读卡器连接已完全关闭
2025-08-05 17:59:38.374506: changeReaders
2025-08-05 17:59:38.375507: createIsolate isOpen:true,isOpening:false
2025-08-05 17:59:38.375507: open():SendPort
2025-08-05 17:59:38.375507: untilDetcted():SendPort
2025-08-05 17:59:38.375507: 读卡器配置完成，共 3 个设备
2025-08-05 17:59:38.376504: 已移除读卡器状态监听器
2025-08-05 17:59:38.376504: 已移除标签数据监听器
2025-08-05 17:59:38.376504: 所有卡片监听器已移除
2025-08-05 17:59:38.376504: 已添加读卡器状态监听器
2025-08-05 17:59:38.376504: 已添加标签数据监听器
2025-08-05 17:59:38.376504: 开始监听卡片数据 - 所有监听器已就绪
2025-08-05 17:59:38.376504: 读卡器认证监听启动成功
2025-08-05 17:59:38.376504: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-05 17:59:38.376504: 所有认证服务启动完成，成功启动 2 个服务
2025-08-05 17:59:38.376504: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-05 17:59:38.377545: MultiAuthManager启动监听成功
2025-08-05 17:59:38.377545: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-05 17:59:38.377545: 闸机状态变更: GateState.enterScanning
2025-08-05 17:59:38.377545: 闸机认证服务启动成功
2025-08-05 17:59:38.377545: subThread :ReaderCommand.close
2025-08-05 17:59:38.377545: commandRsp:ReaderCommand.close
2025-08-05 17:59:38.377545: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:38.377545: close:T10Bridge
2025-08-05 17:59:38.378533: close:HD100SSBridge
2025-08-05 17:59:38.378533: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:38.378533: HD100SS close result: -1
2025-08-05 17:59:38.378533: close:ScanerBridge
2025-08-05 17:59:38.378533: 发送：[16, 55, 0d]
2025-08-05 17:59:38.379495: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:38.379495: 读卡器状态变化: ReaderErrorType.closeFail
2025-08-05 17:59:38.379495: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:38.379495: subThread :ReaderCommand.readerList
2025-08-05 17:59:38.379495: commandRsp:ReaderCommand.readerList
2025-08-05 17:59:38.379495: readerList：3,readerSetting：3
2025-08-05 17:59:38.380493: cacheUsedReaders:3
2025-08-05 17:59:38.380493: subThread :ReaderCommand.open
2025-08-05 17:59:38.380493: commandRsp:ReaderCommand.open
2025-08-05 17:59:38.482817: dc_init:0xb4 100
2025-08-05 17:59:38.483190: open reader readerType ：10 ret：0
2025-08-05 17:59:38.483190: open reader readerType ：13 ret：0
2025-08-05 17:59:38.483190: open reader readerType ：12 ret：0
2025-08-05 17:59:38.483190: [[10, 0], [13, 0], [12, 0]]
2025-08-05 17:59:38.484258: changeType:ReaderErrorType.openSuccess
2025-08-05 17:59:38.484258: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-05 17:59:38.484258: 读卡器连接成功
2025-08-05 17:59:38.484258: 读卡器连接成功，确保扫描状态正常
2025-08-05 17:59:38.484258: 恢复读卡器扫描状态...
2025-08-05 17:59:38.484258: 读卡器扫描状态已恢复
2025-08-05 17:59:38.485750: subThread :ReaderCommand.untilDetected
2025-08-05 17:59:38.486254: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:59:38.486559: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:38.486559: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:38.986736: dc_config_card:0
2025-08-05 17:59:39.002692: dc_card_n_hex:1,len:0
2025-08-05 17:59:39.002692: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:39.649113: iReadCardBas ret:4294967294
2025-08-05 17:59:39.649113: 无卡
2025-08-05 17:59:39.649113: 无卡
2025-08-05 17:59:39.651107: dc_config_card:0
2025-08-05 17:59:39.666870: dc_card_n_hex:1,len:0
2025-08-05 17:59:39.666870: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:39.739126: iReadCardBas ret:0
2025-08-05 17:59:39.739126: C28060635|俞佳|330424198710132426|201703-202703|
2025-08-05 17:59:39.739126: C28060635|俞佳|330424198710132426|201703-202703|
2025-08-05 17:59:39.740159: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: C28060635, cdInfo: null}
2025-08-05 17:59:39.740159: data:330424198710132426,coder:NoParseCoder
2025-08-05 17:59:39.740159: parseRet：{"barCode":"330424198710132426"},decoder:不解析
2025-08-05 17:59:39.741123: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:39.741123: 验证通用二维码: 330424198710132426
2025-08-05 17:59:39.741123: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:39.741123: 验证通用二维码: 330424198710132426
2025-08-05 17:59:39.741123: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:39.742120: 验证通用二维码: 330424198710132426
2025-08-05 17:59:39.742120: 检测到读卡器数据: 1条
2025-08-05 17:59:39.742120: 读卡器数据认证：
2025-08-05 17:59:39.742120:   设备类型: 13
2025-08-05 17:59:39.742120:   条码: 330424198710132426
2025-08-05 17:59:39.742120:   标签UID: 330424198710132426
2025-08-05 17:59:39.742120:   对应登录类型: AuthLoginType.socailSecurityCard
2025-08-05 17:59:39.742120:   根据读卡器类型13确定认证方式为: 社保卡
2025-08-05 17:59:39.742120:   开始调用认证API: 330424198710132426
2025-08-05 17:59:39.742120: 正在认证用户: 330424198710132426, 方式: 社保卡
2025-08-05 17:59:39.742120: 多认证管理器: 切换显示方式 微信扫码 -> 社保卡
2025-08-05 17:59:39.742120: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:39.742120: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:39.743118: 多认证管理器: 社保卡获得认证请求锁
2025-08-05 17:59:39.743118: 63 CardType 值为空
2025-08-05 17:59:39.743118: Req msgType：Sip2MsgType.patronInformation ,length:83， ret:  6300120250805    175939  Y       AOhlsp|AA330424198710132426|TY|BP1|BQ20|AY5AZED94
2025-08-05 17:59:39.743118: 检测到了卡 来暂停 ：1
2025-08-05 17:59:39.835871: Rsp : 64YYYYYYYYYYYYYY00020250805    180114000000000000000000000000AOhlsp|AA330424198710132426|XO|AE330424198710132426|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY5AZBC82
2025-08-05 17:59:39.856756: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250805    180114, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 5AZBC82}
2025-08-05 17:59:39.856756: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250805    180114, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 5AZBC82}
2025-08-05 17:59:39.856756: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
2025-08-05 17:59:39.856756: 认证失败，继续监听新的卡片扫描...
2025-08-05 17:59:39.856756:   认证结果: AuthStatus.failureNoMatch, 方式: 社保卡
2025-08-05 17:59:39.857754: 认证失败，继续监听下一个用户
2025-08-05 17:59:39.857754: 恢复读卡器扫描状态...
2025-08-05 17:59:39.857754: 读卡器扫描状态已恢复
2025-08-05 17:59:39.857754: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-05 17:59:39.857754: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:39.857754: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:39.857754: 多认证管理器状态变更: authenticating
2025-08-05 17:59:39.857754: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-05 17:59:39.858750: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:39.858750: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-05 17:59:39.858750: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-05 17:59:39.858750: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:39.858750: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-05 17:59:39.858750: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-05 17:59:39.858750: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:39.858750: 非认证状态收到认证结果，忽略
2025-08-05 17:59:39.858750: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.859748: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.859748: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:39.859748: 非认证状态收到认证结果，忽略
2025-08-05 17:59:39.859748: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.859748: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.859748: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:39.859748: 非认证状态收到认证结果，忽略
2025-08-05 17:59:39.860745: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.860745: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-05 17:59:39.860745: 页面状态变更: SilencePageState.authFailed
2025-08-05 17:59:39.860745: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:39.860745: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:39.986503: dc_config_card:0
2025-08-05 17:59:40.002560: dc_card_n_hex:1,len:0
2025-08-05 17:59:40.002560: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:40.070644: iReadCardBas ret:0
2025-08-05 17:59:40.071640: C28060635|俞佳|330424198710132426|201703-202703|
2025-08-05 17:59:40.071640: C28060635|俞佳|330424198710132426|201703-202703|
2025-08-05 17:59:40.071640: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: C28060635, cdInfo: null}
2025-08-05 17:59:40.071640: 检测到了卡 来暂停 ：1
2025-08-05 17:59:40.072638: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:40.072638: 验证通用二维码: 330424198710132426
2025-08-05 17:59:40.072638: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:40.072638: 验证通用二维码: 330424198710132426
2025-08-05 17:59:40.072638: 检测到二维码数据: 330424198710132426
2025-08-05 17:59:40.072638: 验证通用二维码: 330424198710132426
2025-08-05 17:59:42.858774: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-05 17:59:42.858774: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:42.859742: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:42.859742: 多认证管理器状态变更: listening
2025-08-05 17:59:44.859113: 多认证管理器: 认证请求锁已释放（之前为社保卡）
2025-08-05 17:59:44.859113: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-05 17:59:44.859113: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:44.859113: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:44.863107: 停止闸机认证服务...
2025-08-05 17:59:44.863107: 闸机状态变更: GateState.idle
2025-08-05 17:59:44.864112: 页面状态变更: SilencePageState.welcome
2025-08-05 17:59:44.864112: 停止所有认证方式监听
2025-08-05 17:59:44.864112: 停止微信扫码认证监听
2025-08-05 17:59:44.864112: 停止二维码扫描认证监听
2025-08-05 17:59:44.864112: 停止读者证认证监听
2025-08-05 17:59:44.864112: 停止读卡器认证监听
2025-08-05 17:59:44.865097: 停止社保卡认证监听
2025-08-05 17:59:44.865097: 停止读卡器认证监听
2025-08-05 17:59:44.865097: 停止电子社保卡认证监听
2025-08-05 17:59:44.866105: 停止读卡器认证监听
2025-08-05 17:59:44.866105: 二维码扫描认证监听已停止
2025-08-05 17:59:44.866105: 微信扫码认证服务监听已停止
2025-08-05 17:59:44.866105: 已移除读卡器状态监听器
2025-08-05 17:59:44.866105: 已移除标签数据监听器
2025-08-05 17:59:44.866105: 所有卡片监听器已移除
2025-08-05 17:59:44.867092: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:44.867092: stopInventory newPort:SendPort
2025-08-05 17:59:44.867092: 已移除读卡器状态监听器
2025-08-05 17:59:44.867092: 已移除标签数据监听器
2025-08-05 17:59:44.867092: 所有卡片监听器已移除
2025-08-05 17:59:44.867092: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:44.867092: stopInventory newPort:SendPort
2025-08-05 17:59:44.867092: 已移除读卡器状态监听器
2025-08-05 17:59:44.867092: 已移除标签数据监听器
2025-08-05 17:59:44.867092: 所有卡片监听器已移除
2025-08-05 17:59:44.867092: 暂停读卡器扫描（保持连接）...
2025-08-05 17:59:44.867092: stopInventory newPort:SendPort
2025-08-05 17:59:44.868089: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:44.868089: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:44.868089: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:44.868089: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:44.868089: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:44.868089: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:44.968417: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:44.969360: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:44.969360: 读者证认证服务监听已停止
2025-08-05 17:59:44.969360: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:44.969360: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:44.969360: 社保卡认证服务监听已停止
2025-08-05 17:59:44.969360: 读卡器扫描已暂停，USB/串口连接保持
2025-08-05 17:59:44.969360: 读卡器认证监听已停止（连接保持）
2025-08-05 17:59:44.970328: 电子社保卡认证服务监听已停止
2025-08-05 17:59:44.970328: 多认证管理器状态变更: idle
2025-08-05 17:59:44.970328: 所有认证方式监听已停止
2025-08-05 17:59:44.970328: 闸机认证服务已停止
2025-08-05 17:59:48.470146: 收到串口命令: enter_start (进馆开始)
2025-08-05 17:59:48.470146: 处理进馆开始
2025-08-05 17:59:48.470146: 闸机状态变更: GateState.enterStarted
2025-08-05 17:59:48.470146: 页面状态变更: SilencePageState.authenticating
2025-08-05 17:59:48.470146: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:48.470146: 启动闸机认证服务...
2025-08-05 17:59:48.470146: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-05 17:59:48.471144: 开始启动MultiAuthManager监听...
2025-08-05 17:59:48.471144: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:48.471144: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:48.471144: 多认证管理器状态变更: listening
2025-08-05 17:59:48.471144: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:59:48.471144: 准备启动 2 个物理认证服务
2025-08-05 17:59:48.471144: 开始二维码扫描认证监听
2025-08-05 17:59:48.471144: 开始二维码扫描监听
2025-08-05 17:59:48.471144: 二维码扫描认证监听启动成功
2025-08-05 17:59:48.471144: 微信扫码 认证服务启动成功
2025-08-05 17:59:48.472141: 开始读卡器认证监听
2025-08-05 17:59:48.472141: 强制重新配置读卡器以确保状态一致性
2025-08-05 17:59:48.472141: 完全重置读卡器连接和监听器状态...
2025-08-05 17:59:48.472141: 已移除读卡器状态监听器
2025-08-05 17:59:48.472141: 已移除标签数据监听器
2025-08-05 17:59:48.472141: 所有卡片监听器已移除
2025-08-05 17:59:48.472141: stopInventory newPort:SendPort
2025-08-05 17:59:48.474135: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:48.474135: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:48.673843: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:48.673843: subThread :ReaderCommand.close
2025-08-05 17:59:48.673843: commandRsp:ReaderCommand.close
2025-08-05 17:59:48.673843: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:48.674804: close:T10Bridge
2025-08-05 17:59:48.674804: dc_exit:0
2025-08-05 17:59:48.674804: close:HD100SSBridge
2025-08-05 17:59:48.674804: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:48.674804: HD100SS close result: -1
2025-08-05 17:59:48.675804: close:ScanerBridge
2025-08-05 17:59:48.675804: 发送：[16, 55, 0d]
2025-08-05 17:59:48.675804: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:48.675804: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:48.774856: 读卡器连接已完全关闭
2025-08-05 17:59:48.775858: 读卡器连接和监听器状态已完全重置
2025-08-05 17:59:48.775858: 添加设备配置: 读者证认证 -> 1个设备
2025-08-05 17:59:48.775858: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-05 17:59:48.775858: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-05 17:59:48.775858: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-05 17:59:48.775858: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-05 17:59:48.775858: 添加有效设备: type=10, id=10
2025-08-05 17:59:48.775858: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-05 17:59:48.775858: 添加有效设备: type=13, id=13
2025-08-05 17:59:48.776855: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-05 17:59:48.776855: 添加有效设备: type=12, id=12
2025-08-05 17:59:48.776855: 总共加载了3个设备配置
2025-08-05 17:59:48.776855: stopInventory newPort:SendPort
2025-08-05 17:59:48.776855: subThread :ReaderCommand.stopInventory
2025-08-05 17:59:48.777627: commandRsp:ReaderCommand.stopInventory
2025-08-05 17:59:48.878545: 发送 关闭 阅读器newPort:SendPort
2025-08-05 17:59:48.878545: 读卡器连接已完全关闭
2025-08-05 17:59:48.878545: changeReaders
2025-08-05 17:59:48.879537: createIsolate isOpen:true,isOpening:false
2025-08-05 17:59:48.879537: open():SendPort
2025-08-05 17:59:48.879537: untilDetcted():SendPort
2025-08-05 17:59:48.879537: 读卡器配置完成，共 3 个设备
2025-08-05 17:59:48.880533: 已移除读卡器状态监听器
2025-08-05 17:59:48.880533: 已移除标签数据监听器
2025-08-05 17:59:48.880533: 所有卡片监听器已移除
2025-08-05 17:59:48.880533: 已添加读卡器状态监听器
2025-08-05 17:59:48.880533: 已添加标签数据监听器
2025-08-05 17:59:48.880533: 开始监听卡片数据 - 所有监听器已就绪
2025-08-05 17:59:48.880533: 读卡器认证监听启动成功
2025-08-05 17:59:48.880533: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-05 17:59:48.881531: 所有认证服务启动完成，成功启动 2 个服务
2025-08-05 17:59:48.881531: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-05 17:59:48.881531: MultiAuthManager启动监听成功
2025-08-05 17:59:48.881531: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-05 17:59:48.881531: 闸机状态变更: GateState.enterScanning
2025-08-05 17:59:48.881531: 闸机认证服务启动成功
2025-08-05 17:59:48.881531: subThread :ReaderCommand.close
2025-08-05 17:59:48.881531: commandRsp:ReaderCommand.close
2025-08-05 17:59:48.882529: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-05 17:59:48.882529: close:T10Bridge
2025-08-05 17:59:48.882529: close:HD100SSBridge
2025-08-05 17:59:48.882529: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-05 17:59:48.883526: HD100SS close result: -1
2025-08-05 17:59:48.883526: close:ScanerBridge
2025-08-05 17:59:48.883526: 发送：[16, 55, 0d]
2025-08-05 17:59:48.883526: changeType:ReaderErrorType.closeFail
2025-08-05 17:59:48.883526: 读卡器状态变化: ReaderErrorType.closeFail
2025-08-05 17:59:48.884543: close error: SerialPortError: Éè±¸²»Ê¶±ð´ËÃüÁî¡£, errno = 22
2025-08-05 17:59:48.884543: subThread :ReaderCommand.readerList
2025-08-05 17:59:48.884543: commandRsp:ReaderCommand.readerList
2025-08-05 17:59:48.884543: readerList：3,readerSetting：3
2025-08-05 17:59:48.884543: cacheUsedReaders:3
2025-08-05 17:59:48.884543: subThread :ReaderCommand.open
2025-08-05 17:59:48.885541: commandRsp:ReaderCommand.open
2025-08-05 17:59:48.987035: dc_init:0xb4 100
2025-08-05 17:59:48.987035: open reader readerType ：10 ret：0
2025-08-05 17:59:48.987035: open reader readerType ：13 ret：0
2025-08-05 17:59:48.987035: open reader readerType ：12 ret：0
2025-08-05 17:59:48.988035: [[10, 0], [13, 0], [12, 0]]
2025-08-05 17:59:48.988035: changeType:ReaderErrorType.openSuccess
2025-08-05 17:59:48.988035: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-05 17:59:48.988035: 读卡器连接成功
2025-08-05 17:59:48.988035: 读卡器连接成功，确保扫描状态正常
2025-08-05 17:59:48.988035: 恢复读卡器扫描状态...
2025-08-05 17:59:48.988035: 读卡器扫描状态已恢复
2025-08-05 17:59:48.989030: subThread :ReaderCommand.untilDetected
2025-08-05 17:59:48.989030: commandRsp:ReaderCommand.untilDetected
2025-08-05 17:59:48.990031: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:48.990031: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:49.490930: dc_config_card:0
2025-08-05 17:59:49.506613: dc_card_n_hex:1,len:0
2025-08-05 17:59:49.506613: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:50.153456: iReadCardBas ret:4294967294
2025-08-05 17:59:50.153456: 无卡
2025-08-05 17:59:50.154469: 无卡
2025-08-05 17:59:50.162982: dc_config_card:0
2025-08-05 17:59:50.250570: dc_card_n_hex:0,len:4
2025-08-05 17:59:50.250570: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-05 17:59:50.259098: dc_authentication_pass:0
2025-08-05 17:59:50.266963: dc_read:0
2025-08-05 17:59:50.266963: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-05 17:59:50.266963: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-05 17:59:50.267964: 检测到二维码数据: 111111
2025-08-05 17:59:50.267964: 未知的二维码类型: 111111
2025-08-05 17:59:50.267964: 检测到二维码数据: 111111
2025-08-05 17:59:50.267964: 未知的二维码类型: 111111
2025-08-05 17:59:50.268960: 检测到二维码数据: 111111
2025-08-05 17:59:50.268960: 未知的二维码类型: 111111
2025-08-05 17:59:50.268960: 检测到二维码数据: 111111
2025-08-05 17:59:50.268960: 未知的二维码类型: 111111
2025-08-05 17:59:50.268960: 检测到读卡器数据: 1条
2025-08-05 17:59:50.268960: 读卡器数据认证：
2025-08-05 17:59:50.268960:   设备类型: 10
2025-08-05 17:59:50.268960:   条码: 111111
2025-08-05 17:59:50.268960:   标签UID: A577EB2D
2025-08-05 17:59:50.268960:   对应登录类型: AuthLoginType.readerCard
2025-08-05 17:59:50.269957:   根据读卡器类型10确定认证方式为: 读者证
2025-08-05 17:59:50.269957:   开始调用认证API: 111111
2025-08-05 17:59:50.269957: 正在认证用户: 111111, 方式: 读者证
2025-08-05 17:59:50.269957: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-05 17:59:50.269957: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:50.269957: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:50.269957: 多认证管理器: 读者证获得认证请求锁
2025-08-05 17:59:50.269957: 63 CardType 值为空
2025-08-05 17:59:50.269957: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250805    175950  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY6AZF010
2025-08-05 17:59:50.270954: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:50.712940: Rsp : 64              00120250805    180125000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY6AZDBBA
2025-08-05 17:59:50.727398: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250805    180125, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBBA}
2025-08-05 17:59:50.727889: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250805    180125, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBBA}
2025-08-05 17:59:50.727889: SIP2认证成功: 用户=gd, ID=111111
2025-08-05 17:59:50.728891:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-05 17:59:50.728891: 认证成功，继续监听下一个用户
2025-08-05 17:59:50.728891: 恢复读卡器扫描状态...
2025-08-05 17:59:50.729886: 读卡器扫描状态已恢复
2025-08-05 17:59:50.729886: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-05 17:59:50.729886: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.729886: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.729886: 多认证管理器状态变更: authenticating
2025-08-05 17:59:50.729886: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-05 17:59:50.729886: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.729886: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.729886: 多认证管理器状态变更: completed
2025-08-05 17:59:50.729886: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-05 17:59:50.730885: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:50.730885: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-05 17:59:50.730885: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.730885: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.730885: 多认证管理器状态变更: authenticating
2025-08-05 17:59:50.730885: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-05 17:59:50.730885: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.730885: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.730885: 多认证管理器状态变更: completed
2025-08-05 17:59:50.730885: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-05 17:59:50.730885: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:50.730885: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-05 17:59:50.731882: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.731882: 认证状态变化: MultiAuthState.authenticating
2025-08-05 17:59:50.731882: 多认证管理器状态变更: authenticating
2025-08-05 17:59:50.731882: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-05 17:59:50.731882: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.731882: 认证状态变化: MultiAuthState.completed
2025-08-05 17:59:50.731882: 多认证管理器状态变更: completed
2025-08-05 17:59:50.731882: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-05 17:59:50.731882: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-05 17:59:50.731882: 非认证状态收到认证结果，忽略
2025-08-05 17:59:50.731882: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-05 17:59:50.732884: 收到闸机认证结果: AuthStatus.success, 用户: gd
2025-08-05 17:59:50.732884: 闸机状态变更: GateState.enterOpening
2025-08-05 17:59:50.732884: 页面状态变更: SilencePageState.authSuccess
2025-08-05 17:59:50.732884: 发送闸机命令不完整: enter_open (7/8 bytes)
2025-08-05 17:59:50.732884: 非认证状态收到认证结果，忽略
2025-08-05 17:59:50.732884: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-05 17:59:50.733824: 非认证状态收到认证结果，忽略
2025-08-05 17:59:50.733824: 非认证状态收到认证结果，忽略
2025-08-05 17:59:50.733824: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-05 17:59:50.733824: 非认证状态收到认证结果，忽略
2025-08-05 17:59:50.913857: iReadCardBas ret:4294967294
2025-08-05 17:59:50.913857: 无卡
2025-08-05 17:59:50.913857: 无卡
2025-08-05 17:59:50.914859: 检测到了卡 来暂停 ：1
2025-08-05 17:59:50.914859: subThread :ReaderCommand.resumeInventory
2025-08-05 17:59:50.914859: commandRsp:ReaderCommand.resumeInventory
2025-08-05 17:59:50.922378: dc_config_card:0
2025-08-05 17:59:51.018034: dc_card_n_hex:0,len:4
2025-08-05 17:59:51.019006: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-05 17:59:51.026945: dc_authentication_pass:0
2025-08-05 17:59:51.034948: dc_read:0
2025-08-05 17:59:51.034948: iReadCardBas: start ,isOpenComPort:false
2025-08-05 17:59:51.681358: iReadCardBas ret:4294967294
2025-08-05 17:59:51.681358: 无卡
2025-08-05 17:59:51.681358: 无卡
2025-08-05 17:59:51.682358: 检测到了卡 来暂停 ：1
2025-08-05 17:59:51.682358: 检测到二维码数据: 111111
2025-08-05 17:59:51.683357: 未知的二维码类型: 111111
2025-08-05 17:59:51.683357: 检测到二维码数据: 111111
2025-08-05 17:59:51.683357: 未知的二维码类型: 111111
2025-08-05 17:59:51.683357: 检测到二维码数据: 111111
2025-08-05 17:59:51.683357: 未知的二维码类型: 111111
2025-08-05 17:59:51.683357: 检测到二维码数据: 111111
2025-08-05 17:59:51.684351: 未知的二维码类型: 111111
2025-08-05 17:59:53.732054: 停止闸机认证服务...
2025-08-05 17:59:53.732054: 闸机状态变更: GateState.idle
2025-08-05 17:59:53.733057: 页面状态变更: SilencePageState.welcome
2025-08-05 17:59:53.733057: 认证监听未在运行中
2025-08-05 17:59:53.733057: 闸机认证服务已停止
2025-08-05 17:59:55.729893: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-05 17:59:55.729893: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-05 17:59:57.581713: 收到串口命令: enter_start (进馆开始)
2025-08-05 17:59:57.581713: 处理进馆开始
2025-08-05 17:59:57.581713: 闸机状态变更: GateState.enterStarted
2025-08-05 17:59:57.581713: 页面状态变更: SilencePageState.authenticating
2025-08-05 17:59:57.582709: MultiAuthManager当前状态: MultiAuthState.completed
2025-08-05 17:59:57.582709: 启动闸机认证服务...
2025-08-05 17:59:57.582709: MultiAuthManager当前状态: MultiAuthState.completed
2025-08-05 17:59:57.582709: 开始启动MultiAuthManager监听...
2025-08-05 17:59:57.582709: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:57.582709: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:57.582709: 多认证管理器状态变更: listening
2025-08-05 17:59:57.582709: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-05 17:59:57.582709: 准备启动 2 个物理认证服务
2025-08-05 17:59:57.582709: 服务已在监听中，跳过启动
2025-08-05 17:59:57.582709: 服务已在监听中，跳过启动
2025-08-05 17:59:57.582709: 多认证管理器错误: 启动认证监听失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.582709: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:57.583742: 认证状态变化: MultiAuthState.listening
2025-08-05 17:59:57.583742: 认证状态变化: MultiAuthState.error
2025-08-05 17:59:57.583742: 认证状态变化: MultiAuthState.error
2025-08-05 17:59:57.583742: 多认证管理器状态变更: error
2025-08-05 17:59:57.583742: 启动认证监听失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.583742: 认证服务错误: 启动认证监听失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.583742: 系统错误: 认证系统错误: 启动认证监听失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.584703: 闸机状态变更: GateState.error
2025-08-05 17:59:57.584703: 页面状态变更: SilencePageState.error
2025-08-05 17:59:57.584703: 闸机认证服务未在运行
2025-08-05 17:59:57.584703: 启动闸机认证服务失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.584703: 启动认证系统失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.584703: 系统错误: 启动认证系统失败: Exception: 没有任何认证服务启动成功
2025-08-05 17:59:57.584703: 页面状态变更: SilencePageState.error
2025-08-05 17:59:57.584703: 闸机认证服务未在运行
2025-08-05 18:00:02.585919: 闸机认证服务未在运行
2025-08-05 18:00:02.585919: 闸机状态变更: GateState.idle
2025-08-05 18:00:02.585919: 页面状态变更: SilencePageState.welcome
2025-08-05 18:00:02.585919: 闸机认证服务未在运行
2025-08-05 18:00:02.585919: 页面状态变更: SilencePageState.welcome
2025-08-05 18:00:03.119372: 手动重置系统
2025-08-05 18:00:03.119372: 闸机认证服务未在运行
2025-08-05 18:00:03.119372: 页面状态变更: SilencePageState.welcome
2025-08-05 18:00:05.327829: 收到串口命令: enter_start (进馆开始)
2025-08-05 18:00:05.327829: 处理进馆开始
2025-08-05 18:00:05.327829: 闸机状态变更: GateState.enterStarted
2025-08-05 18:00:05.327829: 页面状态变更: SilencePageState.authenticating
2025-08-05 18:00:05.327829: MultiAuthManager当前状态: MultiAuthState.error
2025-08-05 18:00:05.327829: MultiAuthManager处于错误状态，无法启动认证
2025-08-05 18:00:05.328825: 启动认证系统失败: Exception: 认证系统处于错误状态，请重启应用
2025-08-05 18:00:05.328825: 系统错误: 启动认证系统失败: Exception: 认证系统处于错误状态，请重启应用
2025-08-05 18:00:05.328825: 闸机状态变更: GateState.error
2025-08-05 18:00:05.328825: 页面状态变更: SilencePageState.error
2025-08-05 18:00:05.328825: 闸机认证服务未在运行
