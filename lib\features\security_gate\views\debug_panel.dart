import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../viewmodels/silence_page_viewmodel.dart';
import '../models/gate_command.dart';
import '../services/gate_auth_service.dart';

/// 调试控制面板
class DebugPanel extends StatelessWidget {
  const DebugPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<SilencePageViewModel>(
      builder: (context, viewModel, child) {
        return Positioned(
          top: 100.p,
          right: 30.p,
          child: Container(
            width: 350.p,
            padding: EdgeInsets.all(20.p),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(15.p),
              border: Border.all(color: Colors.orange, width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '🔧 调试控制面板',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 18.p,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 15.p),
                
                // 进馆流程按钮
                _buildSectionTitle('进馆流程'),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '进馆开始',
                        () => viewModel.simulateSerialCommand(GateCommand.enterStart),
                        Colors.blue,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '进馆结束',
                        () => viewModel.simulateSerialCommand(GateCommand.enterEnd),
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 15.p),
                
                // 出馆流程按钮
                _buildSectionTitle('出馆流程'),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '出馆开始',
                        () => viewModel.simulateSerialCommand(GateCommand.exitStart),
                        Colors.green,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '到达位置',
                        () => viewModel.simulateSerialCommand(GateCommand.positionReached),
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.p),
                _buildDebugButton(
                  context,
                  '出馆结束',
                  () => viewModel.simulateSerialCommand(GateCommand.exitEnd),
                  Colors.green,
                ),
                
                SizedBox(height: 15.p),
                
                // 系统控制按钮
                _buildSectionTitle('系统控制'),
                SizedBox(height: 8.p),
                _buildDebugButton(
                  context,
                  '重置系统',
                  () => viewModel.resetSystem(),
                  Colors.red,
                ),
                
                SizedBox(height: 15.p),
                
                // 当前状态显示
                Container(
                  padding: EdgeInsets.all(15.p),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8.p),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '系统状态',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.p,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.p),
                      _buildStatusItem('闸机状态', viewModel.currentGateState.name),
                      _buildStatusItem('页面状态', viewModel.currentPageState.name),
                      _buildStatusItem('扫描数量', '${viewModel.scannedBarcodes.length}'),
                      _buildAuthStatusSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: TextStyle(
        color: Colors.white,
        fontSize: 14.p,
        fontWeight: FontWeight.w500,
      ),
    );
  }
  
  /// 构建调试按钮
  Widget _buildDebugButton(
    BuildContext context,
    String text,
    VoidCallback onPressed,
    Color color,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 8.p, horizontal: 12.p),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.p),
        ),
        minimumSize: Size(0, 35.p),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.p),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  /// 构建认证状态监控部分
  Widget _buildAuthStatusSection() {
    final authService = GateAuthService.instance;
    final authStatus = authService.getStatus();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8.p),
        Text(
          '认证服务状态',
          style: TextStyle(
            color: Colors.orange,
            fontSize: 12.p,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4.p),
        _buildStatusItem('服务状态', authService.isRunning ? '运行中' : '已停止'),
        _buildStatusItem('初始化', authService.isInitialized ? '已完成' : '未完成'),
        if (authStatus['enabled_methods'] != null)
          _buildStatusItem('认证方式', (authStatus['enabled_methods'] as List).join('、')),
        if (authService.errorMessage != null)
          _buildStatusItem('错误信息', authService.errorMessage!),
      ],
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.p),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12.p,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.p,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
