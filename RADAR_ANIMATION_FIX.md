# 雷达扫描动画修复

## 问题描述
之前的实现是让整个雷达图片旋转，但正确的效果应该是：
- **radar_scan_bg** - 静态底图（不旋转）
- **scanning_effect** - 扫描效果图绕着底图的圆环轨道旋转
- **数字文字** - 保持静态在中心

## 解决方案

### 修改前的实现
```dart
// 错误：整个背景图片旋转
AnimatedBuilder(
  animation: _rotationAnimation,
  builder: (context, child) {
    return Transform.rotate(
      angle: _rotationAnimation.value,
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AssetUtil.fullPath('radar_scan_bg')), // 整个背景旋转
          ),
        ),
      ),
    );
  },
),
```

### 修改后的实现
```dart
Stack(
  alignment: Alignment.center,
  children: [
    // 1. 静态的雷达底图
    Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('radar_scan_bg')), // 静态不动
        ),
      ),
    ),
    
    // 2. 旋转的扫描效果
    AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AssetUtil.fullPath('scanning_effect')), // 扫描效果旋转
              ),
            ),
          ),
        );
      },
    ),
    
    // 3. 静态的数字文字
    Center(
      child: Text(widget.number.toString()),
    ),
  ],
)
```

## 图层结构

### 从底到顶的图层顺序
1. **底层**: `radar_scan_bg` - 静态雷达底图
2. **中层**: `scanning_effect` - 旋转的扫描效果
3. **顶层**: 数字文字 - 静态显示

### 动画效果
- ✅ **radar_scan_bg**: 保持静态，提供圆环轨道背景
- 🔄 **scanning_effect**: 绕着圆环轨道旋转（3秒一圈）
- ✅ **数字文字**: 保持静态在中心位置

## 视觉效果

### 预期效果
```
┌─────────────────────┐
│   radar_scan_bg     │  ← 静态底图
│  ┌───────────────┐  │
│  │ scanning_effect │  ← 在圆环上旋转
│  │       3        │  ← 静态数字
│  └───────────────┘  │
└─────────────────────┘
```

### 动画描述
- 底图显示雷达的圆环结构
- 扫描效果（可能是扇形或线条）沿着圆环轨道旋转
- 数字始终保持在中心位置不动

## 技术实现

### Stack布局优势
- 允许多个图层重叠
- 每个图层可以独立控制动画
- 保持良好的性能

### 动画控制
- 使用相同的`_rotationAnimation`控制扫描效果旋转
- 3秒完整旋转一圈
- 线性动画，无限循环

### 资源文件
- `radar_scan_bg.png` - 雷达底图资源
- `scanning_effect.png` - 扫描效果资源

## 使用场景

### 扫描中状态
- 底图显示雷达圆环
- 扫描效果持续旋转
- 数字显示当前扫描数量

### 检测到未借书籍状态
- 相同的动画效果
- 数字显示未借书籍数量
- 底部显示警告信息

## 总结
现在的实现正确地分离了静态底图和动态扫描效果，创造了更真实的雷达扫描视觉效果。扫描效果绕着底图的圆环轨道旋转，而不是整个图片旋转，符合真实雷达的工作原理。
