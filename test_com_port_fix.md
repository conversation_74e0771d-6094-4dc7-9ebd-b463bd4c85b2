# COM端口冲突修复测试

## 问题描述
微信扫码器(COM4) 与读卡器服务发生串口冲突，导致微信扫码数据被读卡器服务接收但无法正确处理。

## 修复内容

### 1. 临时禁用读卡器默认配置
- 文件: `lib/features/auth/services/card_reader_service.dart`
- 修改: `_generateDefaultReaders()` 方法返回空列表
- 目的: 避免读卡器占用COM4端口

### 2. 修改默认COM端口配置
- 文件: `lib/core/utils/generate_reader_setting_data.dart`
- 修改: 所有默认COM端口从COM1改为COM5
- 目的: 如果读卡器被启用，避免与微信扫码器冲突

### 3. 修改读卡器服务默认端口
- 文件: `lib/features/auth/services/card_reader_service.dart`
- 修改: 默认COM端口配置从COM1改为COM5
- 目的: 确保读卡器不会占用COM4

## 预期效果

### 修复前的问题
```
2025-08-05 17:42:37.401698: 收到：[57, 48, 48, 48, 53, 50, 13]  // "900052"
2025-08-05 17:42:42.049766: 收到：[57, 48, 48, 48, 53, 50, 13]  // "900052"
```
- 微信扫码数据被读卡器服务接收
- 数据无法被微信扫码服务处理
- 认证超时失败

### 修复后的预期
```
// 读卡器服务不再占用COM4
警告: 读卡器配置已临时禁用，避免与微信扫码器串口冲突

// 微信扫码数据被正确处理
微信扫码检测到: 900052
开始微信认证验证...
微信认证成功: 用户xxx
```

## 测试步骤

1. **重启应用**
2. **触发闸机认证**
3. **使用微信扫码**
4. **观察日志**:
   - 应该看到读卡器禁用警告
   - 微信扫码数据应该被正确处理
   - 认证应该成功

## 长期解决方案

1. **硬件分离**: 确保读卡器和微信扫码器使用不同的COM端口
2. **配置管理**: 在系统配置中明确指定各设备的COM端口
3. **冲突检测**: 添加COM端口冲突检测机制

## 回滚方案

如果需要启用读卡器，请：
1. 确保读卡器连接到COM5或其他非COM4端口
2. 取消注释 `_generateDefaultReaders()` 中的读卡器配置
3. 重新编译和部署
