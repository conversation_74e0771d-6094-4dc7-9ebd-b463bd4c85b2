import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:seasetting/seasetting.dart';
import 'package:sea_socket/sea_socket.dart';

import '../../../core/utils/generate_reader_setting_data.dart';
import '../models/auth_result.dart';
import 'auth_service_interface.dart';
import 'multi_auth_manager.dart';

/// 读卡器认证服务
/// 负责身份证、读者证、社保卡等卡片认证的监听和处理
class CardReaderService implements AuthServiceInterface {
  // 状态管理
  bool _isInitialized = false;
  bool _isListening = false;
  String? _errorMessage;

  // 认证冷却机制
  DateTime? _lastAuthTime;
  String? _lastAuthIdentifier;
  static const Duration _authCooldown = Duration(seconds: 2);

  // 读卡器相关
  HWTagProvider? _tagProvider;
  SettingProvider? _settingProvider;
  List<HWReaderSettingData>? _openedReaders;
  StreamSubscription? _readerSubscription;

  // 认证结果流
  final StreamController<AuthResult> _authResultController = 
      StreamController<AuthResult>.broadcast();

  @override
  Stream<AuthResult> get authResultStream => _authResultController.stream;

  @override
  bool get isListening => _isListening;

  @override
  bool get isInitialized => _isInitialized;

  @override
  String? get errorMessage => _errorMessage;

  @override
  Future<void> initialize(BuildContext context) async {
    if (_isInitialized) {
      print('读卡器认证服务已初始化，跳过重复初始化');
      return;
    }

    try {
      print('初始化读卡器认证服务');

      // 获取Providers
      _tagProvider = Provider.of<HWTagProvider>(context, listen: false);
      _settingProvider = Provider.of<SettingProvider>(context, listen: false);
      
      if (_tagProvider == null || _settingProvider == null) {
        throw Exception('无法获取必要的Providers');
      }

      _isInitialized = true;
      _clearError();
      print('读卡器认证服务初始化成功');
    } catch (e) {
      _setError('读卡器认证服务初始化失败: $e');
      throw e;
    }
  }

  @override
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('服务未初始化，请先调用initialize()');
    }

    if (_isListening) {
      print('读卡器认证已在监听中，确保监听器正常工作');
      // 检查监听器是否仍然有效
      await _ensureListenersActive();
      return;
    }

    try {
      print('开始读卡器认证监听');

      // 清理旧的标签列表
      _tagProvider!.clearTagList();

      // 关键修复：始终强制重新配置，确保状态一致性
      // 这避免了第二次进入时的状态不一致问题
      print('强制重新配置读卡器以确保状态一致性');
      await _resetConnectionAndListeners();
      await _configureReaders();

      // 关键修复：在调用_startCardListening之前设置_isListening为true
      _isListening = true;
      
      // 开始监听卡片数据
      _startCardListening();

      _clearError();
      print('读卡器认证监听启动成功');
    } catch (e) {
      _isListening = false; // 确保失败时重置状态
      _setError('启动读卡器认证监听失败: $e');
      print('启动监听失败，尝试恢复: $e');
      
      // 如果启动失败，尝试重置后重新启动
      try {
        await _resetAndRestart();
      } catch (resetError) {
        print('重置重启也失败: $resetError');
        throw Exception('启动读卡器认证监听失败: $e');
      }
    }
  }

  /// 确保监听器处于活跃状态
  Future<void> _ensureListenersActive() async {
    try {
      // 检查TagProvider监听器是否存在
      if (!_tagProvider!.hasListeners) {
        print('TagProvider监听器丢失，重新添加');
        _tagProvider!.addListener(_onCardDataChanged);
      }
      
      // 检查ReaderManager监听器
      if (!ReaderManager.instance.controller.hasListeners) {
        print('ReaderManager监听器丢失，重新添加');
        ReaderManager.instance.controller.addListener(_onReaderStateChanged);
      }
      
      // 如果有连接但没有在扫描，尝试恢复扫描
      if (_openedReaders?.isNotEmpty == true && 
          ReaderManager.instance.isConnectReader) {
        print('连接存在但可能没有扫描，尝试恢复扫描状态');
        await _safeResumeInventory();
      }
    } catch (e) {
      print('确保监听器活跃时出错: $e');
    }
  }

  /// 恢复扫描（当连接已存在时）
  Future<void> _resumeScanning() async {
    try {
      if (_openedReaders?.isNotEmpty == true && ReaderManager.instance.isConnectReader) {
        print('恢复现有读卡器扫描...');
        
        // 先确保停止任何正在进行的扫描
        try {
          await ReaderManager.instance.stopInventory();
          await Future.delayed(const Duration(milliseconds: 100));
        } catch (e) {
          print('停止现有扫描时出错（忽略）: $e');
        }
        
        // 重新开始扫描
        await ReaderManager.instance.startInventory();
        print('读卡器扫描已成功恢复');
      } else {
        print('无法恢复扫描：没有有效的读卡器连接');
        throw Exception('没有有效的读卡器连接');
      }
    } catch (e) {
      print('恢复读卡器扫描失败: $e');
      // 如果恢复失败，尝试重新配置
      throw Exception('恢复扫描失败，需要重新配置: $e');
    }
  }

  @override
  Future<void> stopListening() async {
    if (!_isListening) {
      return;
    }

    try {
      print('停止读卡器认证监听');

      // 停止监听
      await _readerSubscription?.cancel();
      _readerSubscription = null;

      // 移除监听器
      _removeCardListeners();

      // 关键优化：只暂停扫描，保持连接
      await _pauseScanning();

      _isListening = false;
      print('读卡器认证监听已停止（连接保持）');
    } catch (e) {
      // 增强错误处理
      String errorMessage = '停止读卡器认证监听失败: $e';
      
      // 对特定错误类型进行处理
      if (e.toString().contains('WebSocket') || 
          e.toString().contains('SocketException') ||
          e.toString().contains('远程计算机拒绝网络连接')) {
        errorMessage = 'WebSocket连接错误，但读卡器监听已停止';
        print('检测到WebSocket连接错误，忽略并继续停止流程');
        _isListening = false; // 强制设置为已停止
        return; // 不抛出异常
      } else if (e.toString().contains('Failed to lookup symbol') || 
                 e.toString().contains('error code 127') ||
                 e.toString().contains('iCloseCard')) {
        errorMessage = '动态库符号查找失败，但读卡器监听已停止';
        print('检测到动态库符号查找失败，忽略并继续停止流程');
        _isListening = false; // 强制设置为已停止
        return; // 不抛出异常
      }
      
      _setError(errorMessage);
      print('停止监听出错: $e');
      
      // 对于其他类型的错误，仍然设置为已停止状态
      _isListening = false;
    }
  }

  /// 暂停扫描但保持连接
  Future<void> _pauseScanning() async {
    try {
      if (_openedReaders?.isNotEmpty == true && ReaderManager.instance.isConnectReader) {
        print('暂停读卡器扫描（保持连接）...');
        
        // 只停止盘存（扫描），但保持读卡器连接
        await ReaderManager.instance.stopInventory();
        
        // 等待确保命令被处理
        await Future.delayed(const Duration(milliseconds: 100));
        
        print('读卡器扫描已暂停，USB/串口连接保持');
      } else {
        print('没有活跃的读卡器连接需要暂停');
      }
    } catch (e) {
      print('暂停读卡器扫描失败: $e');
      // 暂停失败不抛异常，因为这不是致命错误
    }
  }

  /// 完全关闭读卡器连接
  Future<void> closeConnection() async {
    try {
      print('完全关闭读卡器连接');
      
      // 停止监听
      await stopListening();
      
      // 关闭连接
      await _closeReaders();
      
      print('读卡器连接已完全关闭');
    } catch (e) {
      _setError('关闭读卡器连接失败: $e');
    }
  }

  @override
  Future<void> reset() async {
    await stopListening();
    _clearError();
    
    if (_isInitialized) {
      await startListening();
    }
  }

  @override
  Map<String, dynamic> getStatus() {
    return {
      'service': 'CardReaderService',
      'initialized': _isInitialized,
      'listening': _isListening,
      'readers_count': _openedReaders?.length ?? 0,
      'error': _errorMessage,
      'tag_count': _tagProvider?.tagList.length ?? 0,
    };
  }

  @override
  void dispose() {
    stopListening();
    _authResultController.close();
    _isInitialized = false;
  }

  /// 配置读卡器
  Future<void> _configureReaders() async {
    try {
      // 获取配置的读卡器设备
      final readers = _getConfiguredReaders();
      
      if (readers.isEmpty) {
        print('警告: 没有配置读卡器设备');
        return;
      }

      // 关闭旧的读卡器连接
      await _closeReaders();

      // 配置新的读卡器
      await ReaderManager.instance.changeReaders(jsonEncode(readers));
      await ReaderManager.instance.open();
      await ReaderManager.instance.untilDeteted();

      _openedReaders = readers;
      print('读卡器配置完成，共 ${readers.length} 个设备');
    } catch (e) {
      throw Exception('配置读卡器失败: $e');
    }
  }

  /// 获取配置的读卡器列表
  List<HWReaderSettingData> _getConfiguredReaders() {
    final readers = <HWReaderSettingData>[];
    final readerConfig = _settingProvider!.readerConfigData;
    
    if (readerConfig?.authMap != null) {
      // 添加所有配置中的设备，不再区分类型
      readerConfig!.authMap.forEach((authType, deviceList) {
        if (deviceList.isNotEmpty) {
          print('添加设备配置: $authType -> ${deviceList.length}个设备');
          readers.addAll(deviceList);
        }
      });
    }
    
    // 如果没有配置任何读卡器，生成默认配置
    if (readers.isEmpty) {
      readers.addAll(_generateDefaultReaders());
    }
    
    // 去重并验证配置
    final validReaders = <HWReaderSettingData>[];
    final seenTypes = <int>{};
    
    for (final reader in readers) {
      if (!seenTypes.contains(reader.readerType)) {
        // 确保读卡器配置完整
        final validatedReader = _validateAndFixReaderConfig(reader);
        validReaders.add(validatedReader);
        seenTypes.add(reader.readerType!);
        print('添加有效设备: type=${reader.readerType}, id=${reader.id}');
      }
    }
    
    print('总共加载了${validReaders.length}个设备配置');
    return validReaders;
  }

  /// 生成默认读卡器配置
  List<HWReaderSettingData> _generateDefaultReaders() {
    final defaultReaders = <HWReaderSettingData>[];

    try {
      // 临时禁用读卡器配置，避免与微信扫码器(COM4)冲突
      print('警告: 读卡器配置已临时禁用，避免与微信扫码器串口冲突');
      print('如需启用读卡器，请确保使用不同的COM端口（非COM4）');

      // 注释掉读卡器配置，避免串口冲突
      /*
      // 生成超高频读卡器配置（用于读者证）
      final uhfReader = HWReaderSettingGenerator.generate(
        readerType: 1, // 超高频
        type: 3, // 读者证类型
        customParams: {
          'info': {
            'comPort': 'COM5', // 改为COM5，避免与微信扫码器(COM4)冲突
            'inAnt': '1',
            'readPower': '20',
            'decoderType': '超高频解码器',
          }
        },
      );
      defaultReaders.add(uhfReader);

      // 生成HD100读卡器配置（用于身份证和M1卡）
      final hd100Reader = HWReaderSettingGenerator.generate(
        readerType: 5, // HD100
        selectedCardType: '身份证',
        type: 3, // 读者证类型
      );
      defaultReaders.add(hd100Reader);
      */

      print('读卡器配置已禁用，返回空配置列表');
    } catch (e) {
      print('生成默认读卡器配置失败: $e');
    }

    return defaultReaders; // 返回空列表，禁用读卡器
  }

  /// 验证并修复读卡器配置
  HWReaderSettingData _validateAndFixReaderConfig(HWReaderSettingData reader) {
    try {
      // 确保基本参数有值
      if (reader.id == null) reader.id = reader.readerType;
      if (reader.type == null) reader.type = 3; // 默认为读者证类型
      
      // 检查并修复info配置
      if (reader.info == null) {
        reader.info = _generateDefaultInfo(reader.readerType!);
      } else {
        _fixInfoConfig(reader);
      }
      
      // 确保编码器配置存在
      if (reader.coderConfig == null) {
        reader.coderConfig = HWReaderSettingGenerator.generateCoderConfig(reader.readerType!);
      }
      
      print('验证读卡器配置: 类型=${reader.readerType}, 解码器=${reader.info?.valueForKey('decoderType')}');
      return reader;
    } catch (e) {
      print('验证读卡器配置失败: $e');
      // 如果验证失败，返回一个基础的超高频配置
      return HWReaderSettingGenerator.generate(readerType: 1, type: 3);
    }
  }

  /// 生成默认的info配置
  HWExtraSettingData _generateDefaultInfo(int readerType) {
    switch (readerType) {
      case 1: // 超高频
        return HWUHFInfoData(
          decoderType: '超高频解码器',
          comPort: 'COM5', // 改为COM5，避免与微信扫码器(COM4)冲突
          inAnt: '1',
          readPower: '20',
          uidLen: '4',
        );
      case 5: // HD100
        return HWDecoderExtraData(decoderType: 'HD100身份证解码器');
      case 10: // T10
        return HWT10IDCardInfoData(
          decoderType: 'T10身份证解码器',
          mode: '串口模式',
          index: '1',
        );
      default:
        return HWCommonInfoData(
          decoderType: '通用解码器',
          comPort: 'COM5', // 改为COM5，避免与微信扫码器(COM4)冲突
          inAnt: '1',
        );
    }
  }

  /// 修复info配置中的缺失字段
  void _fixInfoConfig(HWReaderSettingData reader) {
    final info = reader.info!;
    
    // 确保解码器类型存在
    if (info.valueForKey('decoderType') == null || info.valueForKey('decoderType')!.isEmpty) {
      String defaultDecoder = _getDefaultDecoderType(reader.readerType!);
      info.setValueForKey('decoderType', defaultDecoder);
    }
    
    // 对于超高频读卡器，确保基本参数存在
    if (reader.readerType == 1 && info is HWUHFInfoData) {
      if (info.comPort == null || info.comPort!.isEmpty) info.comPort = 'COM5'; // 改为COM5，避免与微信扫码器(COM4)冲突
      if (info.inAnt == null || info.inAnt!.isEmpty) info.inAnt = '1';
      if (info.readPower == null || info.readPower!.isEmpty) info.readPower = '20';
      if (info.uidLen == null || info.uidLen!.isEmpty) info.uidLen = '4';
    }
    
    // 对于RR9299读卡器，确保基本参数存在
    if ((reader.readerType == 7 || reader.readerType == 8) && info is HWRR9299InfoData) {
      if (info.comPort == null || info.comPort!.isEmpty) info.comPort = 'COM5'; // 改为COM5，避免与微信扫码器(COM4)冲突
    }
  }

  /// 获取默认解码器类型
  String _getDefaultDecoderType(int readerType) {
    switch (readerType) {
      case 0: return 'RR9000解码器';
      case 1: return '超高频解码器';
      case 2: return '人脸识别';
      case 4: return '3036解码器';
      case 5: return 'HD100解码器';
      case 6: return '双频解码器';
      case 7:
      case 8: return 'RR9299解码器';
      case 10: return 'T10解码器';
      case 11: return '通用解码器';
      case 12: return '电子社保解码器';
      case 13: return '华大社保解码器';
      default: return '通用解码器';
    }
  }

  /// 开始监听卡片数据
  void _startCardListening() {
    // 关键修复：确保先完全移除旧的监听器，避免重复添加
    _removeCardListeners();
    
    // 验证TagProvider存在
    if (_tagProvider == null) {
      print('错误: TagProvider为空，无法添加监听器');
      throw Exception('TagProvider为空，无法启动监听');
    }
    
    try {
      // 添加读卡器状态监听器
      ReaderManager.instance.controller.addListener(_onReaderStateChanged);
      print('已添加读卡器状态监听器');
      
      // 添加标签数据监听器
      _tagProvider!.addListener(_onCardDataChanged);
      print('已添加标签数据监听器');
      
      print('开始监听卡片数据 - 所有监听器已就绪');
    } catch (e) {
      print('添加监听器时出错: $e');
      // 如果添加失败，清理已添加的监听器
      _removeCardListeners();
      throw Exception('启动卡片监听失败: $e');
    }
  }

  /// 移除卡片监听器
  void _removeCardListeners() {
    try {
      // 移除读卡器状态监听器
      try {
        ReaderManager.instance.controller.removeListener(_onReaderStateChanged);
        print('已移除读卡器状态监听器');
      } catch (e) {
        print('移除读卡器状态监听器时出错（可能未添加）: $e');
      }
      
      // 移除标签数据监听器
      try {
        if (_tagProvider != null) {
          _tagProvider!.removeListener(_onCardDataChanged);
          print('已移除标签数据监听器');
        }
      } catch (e) {
        print('移除标签数据监听器时出错（可能未添加）: $e');
      }
      
      print('所有卡片监听器已移除');
    } catch (e) {
      print('移除监听器时发生意外错误: $e');
      // 即使移除失败也不抛异常，因为这不应该阻塞主流程
    }
  }

  /// 读卡器状态变化监听
  void _onReaderStateChanged() {
    if (!_isListening) return;
    
    final type = ReaderManager.instance.controller.type;
    print('读卡器状态变化: $type');
    
    // 处理特定的状态变化
    if (type == ReaderErrorType.openFail) {
      print('读卡器连接失败，尝试重新连接');
      _handleConnectionError();
    } else if (type == ReaderErrorType.openSuccess) {
      print('读卡器连接成功');
      _handleConnectionSuccess();
    }
  }

  /// 处理连接错误
  Future<void> _handleConnectionError() async {
    try {
      print('处理读卡器连接错误');
      // 等待一段时间后尝试重新连接
      await Future.delayed(const Duration(seconds: 2));
      
      if (_isListening && (!ReaderManager.instance.isConnectReader)) {
        print('尝试重新配置读卡器');
        await _configureReaders();
      }
    } catch (e) {
      print('处理连接错误失败: $e');
    }
  }

  /// 处理连接成功
  Future<void> _handleConnectionSuccess() async {
    try {
      print('读卡器连接成功，确保扫描状态正常');
      // 确保扫描状态正常
      await _safeResumeInventory();
    } catch (e) {
      print('处理连接成功失败: $e');
    }
  }

  /// 卡片数据变化监听
  void _onCardDataChanged() async {
    if (!_isListening || _tagProvider == null) return;

    try {
      // 优先处理读卡器数据，避免重复处理
      if (_tagProvider!.readerList.isNotEmpty && _tagProvider!.type == HWTagType.addedItem) {
        print('检测到读卡器数据: ${_tagProvider!.readerList.length}条');
        await _processReaderData(_tagProvider!.readerList);
        return; // 处理完读卡器数据后直接返回，避免重复处理标签数据
      }

      // 只有在没有读卡器数据时才处理标签数据
      if (_tagProvider!.tagList.isNotEmpty && _tagProvider!.type == HWTagType.addedItem) {
        print('检测到标签数据: ${_tagProvider!.tagList.length}条');
        await _processTagData(_tagProvider!.tagList);
      }
    } catch (e) {
      print('处理卡片数据时发生错误: $e');
      _setError('处理卡片数据失败: $e');

      // 错误后尝试恢复扫描状态
      await _safeResumeInventory();
    }
  }

  /// 处理读卡器数据
  Future<void> _processReaderData(List<HWTagData> readerData) async {
    bool shouldResumeInventory = true;

    for (final reader in readerData) {
      try {
        final result = await _authenticateWithReaderData(reader);
        // 始终发送结果，无论成功还是失败
        if (result != null) {
          _authResultController.add(result);

          // 考勤系统：无论认证成功还是失败，都应该继续监听下一个用户
          if (result.status == AuthStatus.success) {
            print('认证成功，继续监听下一个用户');
            // 成功后也要继续监听，这是考勤系统的基本要求
          } else {
            print('认证失败，继续监听下一个用户');
          }
        } else {
          // 如果返回null，也应该发送一个失败结果
          print('读卡器返回空结果，发送通用失败消息');
          _authResultController.add(AuthResult(
            method: _getAuthMethodFromReaderType(reader.readerType ?? 0),
            status: AuthStatus.failureNoMatch,
            errorMessage: '无法识别的认证数据',
            timestamp: DateTime.now(),
          ));
        }
      } catch (e) {
        print('处理读卡器数据失败: $e');
        // 处理异常时也应该发送一个失败结果
        _authResultController.add(AuthResult(
          method: _getAuthMethodFromReaderType(reader.readerType ?? 0),
          status: AuthStatus.failureError,
          errorMessage: '认证处理出错',
          timestamp: DateTime.now(),
        ));
      }
    }
    
    // 处理完所有数据后，如果需要，恢复读卡器扫描
    if (shouldResumeInventory && _isListening) {
      await _safeResumeInventory();
    }
  }

  /// 处理标签数据
  Future<void> _processTagData(List<HWTagData> tagData) async {
    bool shouldResumeInventory = true;
    
    for (final tag in tagData) {
      try {
        final result = await _authenticateWithTagData(tag);
        // 始终发送结果，无论成功还是失败
        if (result != null) {
          _authResultController.add(result);

          // 考勤系统：无论认证成功还是失败，都应该继续监听下一个用户
          if (result.status == AuthStatus.success) {
            print('认证成功，继续监听下一个用户');
            // 成功后也要继续监听，这是考勤系统的基本要求
          } else {
            print('认证失败，继续监听下一个用户');
          }
        } else {
          // 如果返回null，也应该发送一个失败结果
          print('标签返回空结果，发送通用失败消息');
          _authResultController.add(AuthResult(
            method: _getAuthMethodFromReaderType(tag.readerType ?? 0),
            status: AuthStatus.failureNoMatch,
            errorMessage: '无法识别的标签数据',
            timestamp: DateTime.now(),
          ));
        }
      } catch (e) {
        print('处理标签数据失败: $e');
        // 处理异常时也应该发送一个失败结果
        _authResultController.add(AuthResult(
          method: _getAuthMethodFromReaderType(tag.readerType ?? 0),
          status: AuthStatus.failureError,
          errorMessage: '标签认证处理出错',
          timestamp: DateTime.now(),
        ));
      }
    }
    
    // 处理完所有数据后，如果需要，恢复读卡器扫描
    if (shouldResumeInventory && _isListening) {
      await _safeResumeInventory();
    }
  }

  /// 使用读卡器数据进行认证
  Future<AuthResult?> _authenticateWithReaderData(HWTagData readerData) async {
    try {
      // 打印详细的读卡器数据信息
      print('读卡器数据认证：');
      print('  设备类型: ${readerData.readerType}');
      print('  条码: ${readerData.barCode}');
      print('  标签UID: ${readerData.uid}');
      
      // 获取更多上下文信息
      AuthLoginType? loginType = _getAuthLoginTypeFromReaderType(readerData.readerType ?? 0);
      print('  对应登录类型: ${loginType?.toString() ?? "未知"}');
      
      // 根据读卡器数据类型判断认证方式
      AuthMethod method = AuthMethod.readerCard; // 默认为读者证
      
      // 1. 先检查是否为身份证
      if (readerData.info?.containsKey('idCard') == true) {
        method = AuthMethod.idCard;
      } 
      // 2. 根据设备类型判断认证方式
      else if (readerData.readerType != null) {
        // 根据readerType确定AuthMethod
        method = _getAuthMethodFromReaderType(readerData.readerType!);
        print('  根据读卡器类型${readerData.readerType}确定认证方式为: ${_getAuthMethodDisplayName(method)}');
      }

      // 提取用户标识信息
      String? userId = readerData.barCode;
      String? cardNumber = readerData.info?['cardNumber'];
      
      if (userId?.isNotEmpty == true || cardNumber?.isNotEmpty == true) {
        print('  开始调用认证API: ${userId ?? cardNumber}');
        // 调用认证API验证用户
        final authResult = await _callAuthenticationAPI(
          userId ?? cardNumber!,
          method,
          additionalInfo: readerData.info,
        );
        
        print('  认证结果: ${authResult.status}, 方式: ${_getAuthMethodDisplayName(authResult.method)}');
        return authResult;
      }
      
      print('  无效的读卡器数据: 缺少用户ID或卡号');
      return AuthResult(
        method: method,
        status: AuthStatus.failureNoMatch,
        errorMessage: '无效的读卡器数据',
        timestamp: DateTime.now(),
      );
    } catch (e) {
      print('读卡器数据认证失败: $e');
      return AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.failureError,
        errorMessage: '认证处理失败',
        timestamp: DateTime.now(),
      );
    }
  }

  /// 使用标签数据进行认证
  Future<AuthResult?> _authenticateWithTagData(HWTagData tagData) async {
    try {
      // 打印详细的标签数据信息
      print('标签数据认证：');
      print('  设备类型: ${tagData.readerType}');
      print('  条码: ${tagData.barCode}');
      print('  标签UID: ${tagData.uid}');
      
      // 获取更多上下文信息
      AuthLoginType? loginType = _getAuthLoginTypeFromReaderType(tagData.readerType ?? 0);
      print('  对应登录类型: ${loginType?.toString() ?? "未知"}');
      
      // 根据标签数据类型判断认证方式
      AuthMethod method = AuthMethod.readerCard; // 默认为读者证
      
      // 根据设备类型判断认证方式
      if (tagData.readerType != null) {
        method = _getAuthMethodFromReaderType(tagData.readerType!);
        print('  根据标签读卡器类型${tagData.readerType}确定认证方式为: ${_getAuthMethodDisplayName(method)}');
      }
      
      if (tagData.barCode?.isNotEmpty == true) {
        print('  开始调用认证API: ${tagData.barCode}');
        // 调用认证API验证用户
        final authResult = await _callAuthenticationAPI(
          tagData.barCode!,
          method,
          additionalInfo: tagData.info,
        );
        
        print('  认证结果: ${authResult.status}, 方式: ${_getAuthMethodDisplayName(authResult.method)}');
        return authResult;
      }
      
      print('  无效的标签数据: 缺少条码');
      return AuthResult(
        method: method,
        status: AuthStatus.failureNoMatch,
        errorMessage: '无效的标签数据',
        timestamp: DateTime.now(),
      );
    } catch (e) {
      print('标签数据认证失败: $e');
      return AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.failureError,
        errorMessage: '认证处理失败',
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// 根据读卡器类型获取对应的认证方式
  AuthMethod _getAuthMethodFromReaderType(int readerType) {
    switch (readerType) {
      case 0: // RR9000
      case 1: // 超高频
      case 4: // 3036阅读器
      case 7: // RR9299
      case 8: // RR9299
      case 10: // T10
        return AuthMethod.readerCard; // 读者证认证
        
      case 2: // 人脸识别
        return AuthMethod.face; // 人脸认证
        
      case 5: // HD100
        return AuthMethod.idCard; // 身份证认证
        
      case 9: // 漂流柜门锁
        return AuthMethod.readerCard; // 读者证认证
        
      case 12: // 扫码枪，电子社保卡，也用于微信扫码等
        return AuthMethod.wechatScanQRCode; // 微信扫码认证
        
      case 13: // 华大社保卡
        return AuthMethod.socialSecurityCard; // 社保卡认证
        
      case 3: // 手动输入
      default:
        return AuthMethod.readerCard; // 默认使用读者证认证
    }
  }

  /// 根据读卡器类型获取对应的登录类型
  AuthLoginType? _getAuthLoginTypeFromReaderType(int readerType) {
    switch (readerType) {
      case 0: // RR9000
      case 1: // 超高频
      case 4: // 3036阅读器
      case 7: // RR9299
      case 8: // RR9299
      case 10: // T10
        return AuthLoginType.readerCard; // 读者证认证
        
      case 2: // 人脸识别
        return AuthLoginType.faceAuth; // 人脸认证
        
      case 5: // HD100
        return AuthLoginType.IDCard; // 身份证认证
        
      case 9: // 漂流柜门锁
        return AuthLoginType.readerCard; // 读者证认证
        
      case 12: // 扫码枪，电子社保卡，也用于微信扫码等
        return AuthLoginType.wechatScanQRCode; // 微信扫码认证
        
      case 13: // 华大社保卡
        return AuthLoginType.socailSecurityCard; // 社保卡认证
        
      case 3: // 手动输入
        return AuthLoginType.keyboardInput; // 手动输入
        
      default:
        return null; // 未知类型
    }
  }

  /// 调用认证API
  Future<AuthResult> _callAuthenticationAPI(
    String identifier,
    AuthMethod method, {
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      print('正在认证用户: $identifier, 方式: ${_getAuthMethodDisplayName(method)}');

      // 检查认证冷却时间和重复标识符
      final now = DateTime.now();
      if (_lastAuthTime != null &&
          _lastAuthIdentifier == identifier &&
          now.difference(_lastAuthTime!) < _authCooldown) {
        print('认证请求过于频繁或重复，冷却中... 标识符: $identifier');
        return AuthResult(
          method: method,
          status: AuthStatus.failureNoMatch,
          errorMessage: '请求过于频繁，请稍后再试',
          timestamp: DateTime.now(),
        );
      }

      // 尝试获取认证请求锁
      final multiAuthManager = MultiAuthManager.instance;
      if (!multiAuthManager.tryAcquireRequestLock(method)) {
        // 如果无法获取锁，说明有其他认证正在进行，返回失败结果
        print('无法获取认证请求锁，当前有其他认证正在进行');
        return AuthResult(
          method: method,
          status: AuthStatus.failureNoMatch,
          errorMessage: '当前有其他认证正在进行，请稍后再试',
          timestamp: DateTime.now(),
        );
      }

      // 更新最后认证时间和标识符
      _lastAuthTime = now;
      _lastAuthIdentifier = identifier;

      try {
        // 调用实际的SIP2认证服务
        final readerInfo = await _authenticateWithSip2(identifier);

        if (readerInfo != null && readerInfo.ValidPatron == 'Y') {
          // 认证成功
          print('SIP2认证成功: 用户=${readerInfo.PersonName}, ID=${readerInfo.PatronIdentifier}');

          return AuthResult(
            method: method, // 使用传入的认证方法
            status: AuthStatus.success,
            userId: readerInfo.PatronIdentifier ?? identifier,
            userName: readerInfo.PersonName ?? '认证用户',
            timestamp: DateTime.now(),
          );
        } else {
          // 认证失败 - 用户无效或不存在
          String errorMessage = '';
          if (readerInfo != null) {
            // 使用SIP2返回的具体错误信息
            errorMessage = readerInfo.ScreenMessage?.isNotEmpty == true
                ? readerInfo.ScreenMessage!
                : '用户不存在或无效';
          } else {
            errorMessage = '无法连接到认证服务器';
          }

          print('SIP2认证失败: $errorMessage');

          // 认证失败后，继续监听新的卡片扫描
          print('认证失败，继续监听新的卡片扫描...');

          return AuthResult(
            method: method, // 使用传入的认证方法，保持一致性
            status: AuthStatus.failureNoMatch,
            errorMessage: errorMessage, // 传递具体的错误信息
            timestamp: DateTime.now(),
          );
        }
      } catch (e) {
        String errorMessage = '网络连接错误或系统异常';
        if (e.toString().contains('timeout')) {
          errorMessage = '认证服务响应超时';
        } else if (e.toString().contains('connection')) {
          errorMessage = '无法连接到认证服务';
        }

        print('API调用异常: $errorMessage - $e');

        return AuthResult(
          method: method, // 确保使用传入的认证方法，即使发生异常
          status: AuthStatus.failureError,
          errorMessage: errorMessage,
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      print('认证过程中发生未预期的错误: $e');
      return AuthResult(
        method: method,
        status: AuthStatus.failureError,
        errorMessage: '认证过程中发生未预期的错误',
        timestamp: DateTime.now(),
      );
    }
  }

  /// 使用SIP2进行认证
  Future<Sip2PatronInfoData?> _authenticateWithSip2(String identifier) async {
    try {
      // 调用SIP2认证服务
      final readerInfo = await NewSip2Request.instance.getReaderInfo(
        identifier,
        identifier,
      ).timeout(const Duration(seconds: 10));
      
      return readerInfo;
    } catch (e) {
      print('SIP2认证请求失败: $e');
      return null;
    }
  }

  /// 关闭读卡器
  Future<void> _closeReaders() async {
    try {
      if (_openedReaders?.isNotEmpty == true) {
        await ReaderManager.instance.stopInventory();
        await Future.delayed(const Duration(milliseconds: 100));
        await ReaderManager.instance.close();
        
        // 等待读卡器完全关闭
        await _waitUntilReaderClosed();
        _openedReaders = null;
      }
    } catch (e) {
      print('关闭读卡器失败: $e');
    }
  }

  /// 等待读卡器完全关闭
  Future<void> _waitUntilReaderClosed() async {
    int waitCount = 0;
    const maxWaitCount = 20; // 最多等待2秒
    
    while (ReaderManager.instance.isConnectReader && waitCount < maxWaitCount) {
      await Future.delayed(const Duration(milliseconds: 100));
      waitCount++;
    }
    
    if (ReaderManager.instance.isConnectReader) {
      print('警告: 读卡器连接未在预期时间内关闭');
    } else {
      print('读卡器连接已完全关闭');
    }
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    print('读卡器认证服务错误: $error');
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 获取认证方式的中文显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '芝麻信用码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海随申码';
      case AuthMethod.keyboardInput:
        return '手动输入';
      case AuthMethod.tencentTCard:
        return '腾讯E证通';
      case AuthMethod.imiAuth:
        return 'IMI身份认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信/支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用认证';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }

  /// 安全地恢复读卡器扫描
  Future<void> _safeResumeInventory() async {
    try {
      // 考勤系统：优先尝试恢复扫描，不要过度依赖连接状态检查
      if (_isListening) {
        print('恢复读卡器扫描状态...');

        // 第一步：尝试直接恢复扫描
        try {
          final resumeResult = ReaderManager.instance.resumeInventory();
          if (resumeResult == 0) {
            print('读卡器扫描状态已恢复');
            // 等待一小段时间确保扫描真正开始
            await Future.delayed(const Duration(milliseconds: 300));
            return; // 成功恢复，直接返回
          } else {
            throw Exception('恢复扫描返回错误码: $resumeResult');
          }
        } catch (resumeError) {
          print('直接恢复扫描失败: $resumeError，尝试重新启动扫描');

          // 第二步：尝试重新启动扫描
          try {
            final startResult = ReaderManager.instance.startInventory();
            if (startResult == 0) {
              print('读卡器扫描已重新启动');
              await Future.delayed(const Duration(milliseconds: 300));
              return; // 成功重启，直接返回
            } else {
              throw Exception('启动扫描返回错误码: $startResult');
            }
          } catch (startError) {
            print('重新启动扫描也失败: $startError');

            // 第三步：检查是否是网络连接问题，如果是则忽略错误继续运行
            if (startError.toString().contains('WebSocket') ||
                startError.toString().contains('Connection') ||
                startError.toString().contains('172.16.1.103') ||
                startError.toString().contains('HttpException')) {
              print('检测到网络读卡器连接问题，但USB读卡器可能仍正常工作，继续监听');
              // 网络读卡器连接问题不影响USB读卡器，继续运行
              return;
            }

            print('尝试完全重新配置读卡器');
          }
        }

        // 第四步：如果上述方法都失败，检查连接状态并尝试重新连接
        if (!ReaderManager.instance.isConnectReader) {
          print('检测到读卡器连接丢失，尝试重新连接...');
          await _handleConnectionLoss();
        } else {
          print('读卡器连接正常，但扫描恢复失败，尝试强制重新配置');
          await _forceReconfigureReaders();
        }
      } else {
        print('服务未在监听状态，跳过扫描恢复');
      }
    } catch (e) {
      print('恢复读卡器扫描失败: $e');

      // 如果是网络连接问题，不要阻塞整个系统
      if (e.toString().contains('WebSocket') ||
          e.toString().contains('Connection') ||
          e.toString().contains('172.16.1.103') ||
          e.toString().contains('HttpException')) {
        print('网络连接问题，但不影响USB读卡器继续工作');
        return;
      }

      // 其他错误才尝试重置连接
      await _handleInventoryError(e);
    }
  }

  /// 处理连接丢失的情况
  Future<void> _handleConnectionLoss() async {
    try {
      print('处理读卡器连接丢失...');
      
      // 重新配置读卡器（保持连接，只重启扫描）
      if (_openedReaders?.isNotEmpty == true) {
        await _reconfigureReadersWithoutClosing();
      }
    } catch (e) {
      print('重新连接读卡器失败: $e');
      _setError('读卡器连接丢失，请检查设备连接');
    }
  }

  /// 重新配置读卡器而不关闭现有连接
  Future<void> _reconfigureReadersWithoutClosing() async {
    try {
      if (_openedReaders?.isNotEmpty == true) {
        print('重新配置读卡器（保持连接）...');
        
        // 尝试重新开始扫描
        await ReaderManager.instance.startInventory();
        print('读卡器扫描已重新启动');
      }
    } catch (e) {
      print('重新配置读卡器失败，尝试完全重新连接: $e');
      // 如果简单重启失败，进行完全重新配置
      await _configureReaders();
    }
  }

  /// 处理网络连接错误
  Future<void> _handleNetworkConnectionError() async {
    try {
      print('处理网络读卡器连接错误...');

      // 等待网络稳定
      await Future.delayed(const Duration(seconds: 2));

      // 尝试完全重新配置读卡器
      await _forceReconfigureReaders();

      print('网络连接错误处理完成');
    } catch (e) {
      print('处理网络连接错误失败: $e');
      _setError('网络读卡器连接失败，请检查网络连接');
    }
  }

  /// 强制重新配置读卡器
  Future<void> _forceReconfigureReaders() async {
    try {
      print('强制重新配置读卡器...');

      // 停止当前扫描
      try {
        ReaderManager.instance.stopInventory();
        await Future.delayed(const Duration(milliseconds: 300));
      } catch (e) {
        print('停止扫描时出错（忽略）: $e');
      }

      // 关闭现有连接
      try {
        ReaderManager.instance.close();
        await _waitUntilReaderClosed();
      } catch (e) {
        print('关闭连接时出错（忽略）: $e');
      }

      // 等待系统稳定
      await Future.delayed(const Duration(seconds: 1));

      // 重新配置
      await _configureReaders();

      // 重新开始监听
      _startCardListening();

      print('强制重新配置完成');
    } catch (e) {
      print('强制重新配置失败: $e');
      throw Exception('强制重新配置读卡器失败: $e');
    }
  }

  /// 处理扫描错误
  Future<void> _handleInventoryError(dynamic error) async {
    try {
      print('处理扫描错误: $error');

      // 根据错误类型决定恢复策略
      if (error.toString().contains('timeout') ||
          error.toString().contains('超时')) {
        // 超时错误，尝试重启扫描
        print('检测到超时错误，重启扫描...');
        await _restartInventoryOnly();
      } else if (error.toString().contains('WebSocket') ||
                 error.toString().contains('Connection') ||
                 error.toString().contains('172.16.1.103')) {
        // 网络连接错误，特殊处理
        print('检测到网络连接错误，重新配置读卡器...');
        await _handleNetworkConnectionError();
      } else if (error.toString().contains('connection') ||
                 error.toString().contains('连接')) {
        // 一般连接错误，重新配置
        print('检测到连接错误，重新配置读卡器...');
        await _handleConnectionLoss();
      } else {
        // 其他错误，尝试简单恢复
        print('未知错误，尝试简单恢复...');
        await _restartInventoryOnly();
      }
    } catch (e) {
      print('错误恢复失败: $e');
      _setError('读卡器出现错误，请重启应用');
    }
  }

  /// 仅重启扫描（不重新配置连接）
  Future<void> _restartInventoryOnly() async {
    try {
      // 停止当前扫描
      await ReaderManager.instance.stopInventory();
      
      // 等待一小段时间
      await Future.delayed(const Duration(milliseconds: 200));
      
      // 重新开始扫描
      await ReaderManager.instance.startInventory();
      print('读卡器扫描已重新启动');
    } catch (e) {
      print('重启扫描失败: $e');
      throw e;
    }
  }

  /// 重置并重新启动
  Future<void> _resetAndRestart() async {
    try {
      print('重置读卡器服务并重新启动...');
      
      // 清理状态
      _isListening = false;
      _clearError();
      
      // 如果有连接，先暂停
      if (ReaderManager.instance.isConnectReader) {
        await _pauseScanning();
      }
      
      // 等待一段时间让系统稳定
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 重新配置
      await _configureReaders();
      
      // 重新开始监听
      _startCardListening();
      _isListening = true;
      
      print('读卡器服务重置完成');
    } catch (e) {
      print('重置读卡器服务失败: $e');
      throw e;
    }
  }

  /// 完全重置连接和监听器状态
  Future<void> _resetConnectionAndListeners() async {
    try {
      print('完全重置读卡器连接和监听器状态...');
      
      // 移除所有监听器（使用正确的方法）
      _removeCardListeners();
      
      // 停止所有扫描
      try {
        await ReaderManager.instance.stopInventory();
        await Future.delayed(const Duration(milliseconds: 200));
      } catch (e) {
        print('停止扫描时出错（忽略）: $e');
      }
      
      // 关闭所有连接
      try {
        await ReaderManager.instance.close();
        await _waitUntilReaderClosed();
      } catch (e) {
        print('关闭连接时出错（忽略）: $e');
      }
      
      // 清理标签列表，但保留_openedReaders状态以便后续判断
      _tagProvider?.clearTagList();
      // 注释掉：不清空_openedReaders，保持状态一致性
      // _openedReaders?.clear();
      
      print('读卡器连接和监听器状态已完全重置');
    } catch (e) {
      print('重置连接和监听器状态失败: $e');
      throw e;
    }
  }
} 
