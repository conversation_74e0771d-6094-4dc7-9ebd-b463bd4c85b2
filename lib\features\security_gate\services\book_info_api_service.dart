import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

import '../models/book_info.dart';

/// 书籍信息API服务
/// 负责从后端API获取书籍详细信息和借阅状态
class BookInfoApiService {
  static BookInfoApiService? _instance;
  static BookInfoApiService get instance => _instance ??= BookInfoApiService._();
  BookInfoApiService._();
  
  // HTTP客户端
  final Dio _dio = Dio();
  
  // 缓存管理
  final Map<String, BookInfo> _bookInfoCache = {};
  final Map<String, DateTime> _cacheTime = {};
  final Duration _cacheExpiry = Duration(hours: 1);
  final int _maxCacheSize = 1000;
  
  // 请求配置
  String _baseUrl = 'http://your-api-server.com'; // 替换为实际API地址
  final Duration _timeout = Duration(seconds: 5);
  final int _maxRetries = 3;
  final Duration _retryDelay = Duration(milliseconds: 500);
  
  // 并发控制
  final Map<String, Future<BookInfo?>> _pendingRequests = {};
  final int _maxConcurrentRequests = 10;
  int _currentRequests = 0;
  
  /// 初始化服务
  void initialize({
    String? baseUrl,
    Duration? timeout,
  }) {
    _baseUrl = baseUrl ?? _baseUrl;
    
    // 配置Dio
    _dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: timeout ?? _timeout,
      receiveTimeout: timeout ?? _timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    
    debugPrint('书籍API服务初始化完成: $_baseUrl');
  }
  
  /// 获取单本书籍信息
  Future<BookInfo?> getBookInfo(String barcode) async {
    if (barcode.isEmpty) return null;
    
    // 检查缓存
    if (_isCacheValid(barcode)) {
      debugPrint('从缓存获取书籍信息: $barcode');
      return _bookInfoCache[barcode];
    }
    
    // 检查是否已有相同请求
    if (_pendingRequests.containsKey(barcode)) {
      debugPrint('等待现有请求完成: $barcode');
      return await _pendingRequests[barcode]!;
    }
    
    // 并发控制
    if (_currentRequests >= _maxConcurrentRequests) {
      await _waitForAvailableSlot();
    }
    
    // 创建新请求
    final future = _requestBookInfoWithRetry(barcode);
    _pendingRequests[barcode] = future;
    _currentRequests++;
    
    try {
      final result = await future;
      
      // 更新缓存
      if (result != null) {
        _updateCache(barcode, result);
      }
      
      return result;
    } finally {
      _pendingRequests.remove(barcode);
      _currentRequests--;
    }
  }
  
  /// 批量获取书籍信息
  Future<List<BookInfo>> getBooksInfo(List<String> barcodes) async {
    if (barcodes.isEmpty) return [];
    
    try {
      debugPrint('批量获取书籍信息，数量: ${barcodes.length}');
      
      // 分离缓存命中和需要请求的条码
      final cachedBooks = <BookInfo>[];
      final needRequestBarcodes = <String>[];
      
      for (String barcode in barcodes) {
        if (_isCacheValid(barcode)) {
          cachedBooks.add(_bookInfoCache[barcode]!);
        } else {
          needRequestBarcodes.add(barcode);
        }
      }
      
      debugPrint('缓存命中: ${cachedBooks.length}, 需要请求: ${needRequestBarcodes.length}');
      
      // 批量请求未缓存的书籍信息
      List<BookInfo> requestedBooks = [];
      if (needRequestBarcodes.isNotEmpty) {
        requestedBooks = await _batchRequestBooksInfo(needRequestBarcodes);
        
        // 更新缓存
        for (BookInfo book in requestedBooks) {
          _updateCache(book.barcode, book);
        }
      }
      
      // 合并结果
      final allBooks = [...cachedBooks, ...requestedBooks];
      debugPrint('批量获取完成，总数: ${allBooks.length}');
      
      return allBooks;
    } catch (e) {
      debugPrint('批量获取书籍信息失败: $e');
      return [];
    }
  }
  
  /// 带重试的单个书籍信息请求
  Future<BookInfo?> _requestBookInfoWithRetry(String barcode) async {
    Exception? lastException;
    
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        debugPrint('请求书籍信息: $barcode (第${attempt}次尝试)');
        return await _requestSingleBookInfo(barcode);
      } catch (e) {
        lastException = e as Exception;
        debugPrint('请求失败: $barcode, 错误: $e');
        
        if (attempt < _maxRetries) {
          // 指数退避重试
          final delay = Duration(
            milliseconds: _retryDelay.inMilliseconds * attempt
          );
          await Future.delayed(delay);
        }
      }
    }
    
    debugPrint('请求书籍信息最终失败: $barcode, 错误: $lastException');
    return null;
  }
  
  /// 单个书籍信息请求
  Future<BookInfo?> _requestSingleBookInfo(String barcode) async {
    try {
      final response = await _dio.post(
        '/api/books/info',
        data: {'barcode': barcode},
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return BookInfo.fromJson(response.data);
      } else {
        throw Exception('API返回错误状态: ${response.statusCode}');
      }
    } catch (e) {
      // 如果API调用失败，返回模拟数据用于测试
      debugPrint('API调用失败，返回模拟数据: $barcode, 错误: $e');
      return _generateMockBookInfo(barcode);
    }
  }
  
  /// 批量请求书籍信息
  Future<List<BookInfo>> _batchRequestBooksInfo(List<String> barcodes) async {
    try {
      final response = await _dio.post(
        '/api/books/batch-info',
        data: {'barcodes': barcodes},
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> data = response.data['books'] ?? [];
        return data.map((item) => BookInfo.fromJson(item)).toList();
      } else {
        throw Exception('批量API返回错误状态: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('批量请求失败，回退到单个请求: $e');
      
      // 回退到单个请求
      final books = <BookInfo>[];
      for (String barcode in barcodes) {
        try {
          final book = await _requestSingleBookInfo(barcode);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          debugPrint('单个请求也失败: $barcode, $e');
        }
      }
      return books;
    }
  }
  
  /// 生成模拟书籍信息（用于测试）
  BookInfo _generateMockBookInfo(String barcode) {
    final random = Random();
    final bookNumber = barcode.replaceAll(RegExp(r'[^0-9]'), '');
    final isBorrowed = random.nextDouble() > 0.3; // 70%概率已借
    
    return BookInfo(
      barcode: barcode,
      bookName: '图书名称_$bookNumber',
      author: '作者${random.nextInt(100)}',
      isbn: '978-${random.nextInt(1000000000)}',
      isBorrowed: isBorrowed,
      borrowDate: isBorrowed 
          ? DateTime.now().subtract(Duration(days: random.nextInt(30)))
          : null,
      returnDate: isBorrowed
          ? DateTime.now().add(Duration(days: random.nextInt(30)))
          : null,
      borrowerName: isBorrowed ? '借阅者${random.nextInt(100)}' : null,
      borrowerId: isBorrowed ? 'USER${random.nextInt(10000)}' : null,
    );
  }
  
  /// 等待可用请求槽位
  Future<void> _waitForAvailableSlot() async {
    while (_currentRequests >= _maxConcurrentRequests) {
      await Future.delayed(Duration(milliseconds: 100));
    }
  }
  
  /// 缓存有效性检查
  bool _isCacheValid(String barcode) {
    if (!_bookInfoCache.containsKey(barcode)) return false;
    
    final cacheTime = _cacheTime[barcode];
    if (cacheTime == null) return false;
    
    return DateTime.now().difference(cacheTime) < _cacheExpiry;
  }
  
  /// 更新缓存
  void _updateCache(String barcode, BookInfo bookInfo) {
    _bookInfoCache[barcode] = bookInfo;
    _cacheTime[barcode] = DateTime.now();
    
    // 清理过期缓存
    _cleanupExpiredCache();
  }
  
  /// 清理过期缓存
  void _cleanupExpiredCache() {
    if (_bookInfoCache.length <= _maxCacheSize) return;
    
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    _cacheTime.forEach((barcode, time) {
      if (now.difference(time) > _cacheExpiry) {
        expiredKeys.add(barcode);
      }
    });
    
    // 移除过期项
    for (final key in expiredKeys) {
      _bookInfoCache.remove(key);
      _cacheTime.remove(key);
    }
    
    // 如果还是太多，移除最旧的项
    if (_bookInfoCache.length > _maxCacheSize) {
      final sortedEntries = _cacheTime.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));
      
      final toRemove = sortedEntries.take(_maxCacheSize ~/ 2);
      for (final entry in toRemove) {
        _bookInfoCache.remove(entry.key);
        _cacheTime.remove(entry.key);
      }
    }
    
    debugPrint('缓存清理完成，当前大小: ${_bookInfoCache.length}');
  }
  
  /// 清空缓存
  void clearCache() {
    _bookInfoCache.clear();
    _cacheTime.clear();
    debugPrint('书籍信息缓存已清空');
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'base_url': _baseUrl,
      'timeout_seconds': _timeout.inSeconds,
      'cache_size': _bookInfoCache.length,
      'max_cache_size': _maxCacheSize,
      'current_requests': _currentRequests,
      'max_concurrent_requests': _maxConcurrentRequests,
      'service_name': 'BookInfoApiService',
      'version': '1.0.0',
    };
  }
  
  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      final response = await _dio.get('/api/health').timeout(Duration(seconds: 5));
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('API连接测试失败: $e');
      return false;
    }
  }
}
