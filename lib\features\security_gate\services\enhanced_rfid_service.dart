import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:seasetting/seasetting.dart';

import '../models/book_info.dart';
import 'book_info_api_service.dart';

/// 增强的RFID扫描服务
/// 借鉴 sea_mini_smart_library_client 的硬件管理技术
/// 集成书籍信息获取功能
class EnhancedRFIDService {
  static EnhancedRFIDService? _instance;
  static EnhancedRFIDService get instance => _instance ??= EnhancedRFIDService._();
  EnhancedRFIDService._();
  
  // 🔥 借鉴：连接缓存机制
  static final Map<String, dynamic> _connectionCache = {};
  static final Map<String, DateTime> _connectionLastUsed = {};
  static final Map<String, bool> _connectionHealth = {};
  
  // 🔥 借鉴：硬件服务
  final HardwareService _hardwareService = HardwareService();
  
  // 书籍信息API服务
  final BookInfoApiService _bookInfoService = BookInfoApiService.instance;
  
  // 阅读器配置
  List<HWReaderSettingData> _readerConfigs = [];
  
  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;
  String? _errorMessage;
  
  // 扫描结果
  final List<String> _scannedBarcodes = [];
  final Map<String, BookInfo> _bookInfoCache = {};
  final Set<String> _processedBarcodes = {};
  
  // 事件流
  final StreamController<String> _barcodeController = 
      StreamController<String>.broadcast();
  Stream<String> get barcodeStream => _barcodeController.stream;
  
  final StreamController<BookScanResult> _bookResultController = 
      StreamController<BookScanResult>.broadcast();
  Stream<BookScanResult> get bookResultStream => _bookResultController.stream;
  
  final StreamController<int> _countController = 
      StreamController<int>.broadcast();
  Stream<int> get countStream => _countController.stream;
  
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;
  
  // 模拟扫描定时器（实际项目中删除）
  Timer? _mockScanTimer;
  
  // Getters
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String? get errorMessage => _errorMessage;
  int get scannedCount => _scannedBarcodes.length;
  List<String> get scannedBarcodes => List.unmodifiable(_scannedBarcodes);
  
  /// 🔥 借鉴：从SettingProvider加载配置
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('增强RFID服务已经初始化');
      return;
    }
    
    try {
      debugPrint('开始初始化增强RFID服务...');
      
      // 🔥 借鉴：获取阅读器配置
      _readerConfigs = Get.context?.read<SettingProvider>()
          .readerConfigData?.bookReaders ?? [];
      
      if (_readerConfigs.isEmpty) {
        debugPrint('警告: 未找到书籍阅读器配置，使用默认配置');
        _readerConfigs = _generateDefaultReaderConfig();
      }
      
      // 🔥 借鉴：初始化硬件服务
      await _hardwareService.initHardware(_readerConfigs);
      
      // 初始化书籍API服务
      _bookInfoService.initialize();
      
      _isInitialized = true;
      _clearError();
      debugPrint('增强RFID服务初始化完成，配置了${_readerConfigs.length}个阅读器');
    } catch (e) {
      final errorMsg = '增强RFID服务初始化失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }
  
  /// 🔥 借鉴：生成默认阅读器配置
  List<HWReaderSettingData> _generateDefaultReaderConfig() {
    // 这里可以根据实际需要配置默认的阅读器
    // 暂时返回空列表，使用模拟扫描
    return [];
  }
  
  /// 开始增强扫描
  Future<void> startEnhancedScanning() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_isScanning) {
      debugPrint('增强RFID扫描已在进行中');
      return;
    }
    
    try {
      debugPrint('开始增强RFID扫描');
      
      // 清空之前的扫描结果
      _scannedBarcodes.clear();
      _bookInfoCache.clear();
      _processedBarcodes.clear();
      
      if (_readerConfigs.isNotEmpty) {
        // 🔥 借鉴：配置阅读器
        await ReaderManager.instance.changeReaders(
          jsonEncode(_readerConfigs.map((e) => e.toJson()).toList())
        );
        
        // 🔥 借鉴：智能连接（网口优先，串口备用）
        await _smartConnect();
        
        // 启动扫描
        await ReaderManager.instance.startInventory();
        
        // 🔥 借鉴：监听标签数据
        _startTagDataListening();
      } else {
        // 使用模拟扫描进行测试
        _startMockScanning();
      }
      
      _isScanning = true;
      _clearError();
      debugPrint('增强RFID扫描已启动');
    } catch (e) {
      final errorMsg = '启动增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }
  
  /// 🔥 借鉴：智能连接逻辑
  Future<void> _smartConnect() async {
    // 检查是否有网口配置
    final networkConfig = _findNetworkConfig();
    
    if (networkConfig != null) {
      debugPrint('使用网口连接: ${networkConfig['ipAddress']}:${networkConfig['port']}');
      await _connectWithNetwork(networkConfig);
    } else {
      debugPrint('使用串口连接');
      await _connectWithSerial();
    }
  }
  
  /// 🔥 借鉴：网口配置检测
  Map<String, String>? _findNetworkConfig() {
    for (HWReaderSettingData config in _readerConfigs) {
      if (config.info is HWUHFInfoData) {
        HWUHFInfoData info = config.info as HWUHFInfoData;
        String? ipAddress = info.valueForKey('ipAddress');
        String? netPort = info.valueForKey('netPort');
        
        if (ipAddress?.isNotEmpty == true && netPort?.isNotEmpty == true) {
          return {
            'ipAddress': ipAddress!,
            'port': netPort!,
            'readerId': config.id.toString(),
          };
        }
      }
    }
    return null;
  }
  
  /// 🔥 借鉴：网口连接（带缓存）
  Future<void> _connectWithNetwork(Map<String, String> config) async {
    final cacheKey = '${config['ipAddress']}:${config['port']}';
    
    // 检查缓存
    if (_connectionCache.containsKey(cacheKey) && 
        _isConnectionHealthy(cacheKey)) {
      debugPrint('复用网口连接缓存: $cacheKey');
      return;
    }
    
    // 创建新连接
    try {
      await ReaderManager.instance.open();
      await ReaderManager.instance.untilDeteted();
      
      // 更新缓存
      _connectionCache[cacheKey] = true;
      _connectionLastUsed[cacheKey] = DateTime.now();
      _connectionHealth[cacheKey] = true;
      
      debugPrint('网口连接成功: $cacheKey');
    } catch (e) {
      _connectionHealth[cacheKey] = false;
      throw Exception('网口连接失败: $e');
    }
  }
  
  /// 🔥 借鉴：串口连接（带缓存）
  Future<void> _connectWithSerial() async {
    try {
      await ReaderManager.instance.open();
      await ReaderManager.instance.untilDeteted();
      debugPrint('串口连接成功');
    } catch (e) {
      throw Exception('串口连接失败: $e');
    }
  }
  
  /// 🔥 借鉴：标签数据监听
  void _startTagDataListening() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null) {
      tagProvider.addListener(_onTagDataChanged);
      debugPrint('已添加标签数据监听器');
    } else {
      debugPrint('警告: 无法获取HWTagProvider');
    }
  }
  
  /// 🔥 借鉴：标签数据处理
  void _onTagDataChanged() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null && tagProvider.tagList.isNotEmpty && tagProvider.type == HWTagType.addedItem) {
      debugPrint('检测到标签数据: ${tagProvider.tagList.length}条');
      
      for (HWTagData tag in tagProvider.tagList) {
        // 🔥 借鉴：条码提取逻辑
        List<String> barcodes = _extractBarcodes(tag);
        for (String barcode in barcodes) {
          if (barcode.isNotEmpty && !_processedBarcodes.contains(barcode)) {
            _processedBarcodes.add(barcode);
            _onBarcodeScanned(barcode);
          }
        }
      }
    }
  }
  
  /// 🔥 借鉴：多字段条码提取
  List<String> _extractBarcodes(HWTagData tag) {
    List<String> barcodes = [];
    
    // 从barCode字段提取
    if (tag.barCode?.isNotEmpty ?? false) {
      barcodes.add(tag.barCode!);
    }
    
    // 从oidList提取
    if (tag.oidList?.isNotEmpty ?? false) {
      for (var oid in tag.oidList!) {
        if (oid.oid == 1 && oid.data.isNotEmpty) {
          barcodes.add(oid.data);
        }
      }
    }
    
    // 从info字段提取
    if (tag.info?.containsKey('barCode') ?? false) {
      String? infoBarcode = tag.info!['barCode'];
      if (infoBarcode?.isNotEmpty ?? false) {
        barcodes.add(infoBarcode!);
      }
    }
    
    // 从uid字段提取
    if (tag.uid?.isNotEmpty ?? false) {
      barcodes.add(tag.uid!);
    }
    
    return barcodes.where((barcode) => barcode.isNotEmpty).toList();
  }
  
  /// 处理扫描到的条码
  void _onBarcodeScanned(String barcode) {
    if (!_isScanning) return;
    
    // 避免重复扫描
    if (_scannedBarcodes.contains(barcode)) return;
    
    _scannedBarcodes.add(barcode);
    debugPrint('扫描到条码: $barcode (总计: ${_scannedBarcodes.length})');
    
    // 发送条码事件
    _barcodeController.add(barcode);
    _countController.add(_scannedBarcodes.length);
    
    // 🔥 异步获取书籍信息
    _fetchBookInfoAsync(barcode);
  }
  
  /// 异步获取书籍信息
  void _fetchBookInfoAsync(String barcode) async {
    try {
      // 先发送处理中的结果
      _bookResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.processing,
      ));
      
      // 获取书籍信息
      final bookInfo = await _bookInfoService.getBookInfo(barcode);
      
      if (bookInfo != null) {
        _bookInfoCache[barcode] = bookInfo;
        debugPrint('获取书籍信息成功: ${bookInfo.bookName} - ${bookInfo.author}');
        
        // 发送成功结果
        _bookResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: bookInfo,
          scanTime: DateTime.now(),
          status: BookScanStatus.success,
        ));
      } else {
        debugPrint('获取书籍信息失败: $barcode');
        
        // 发送失败结果
        _bookResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: null,
          scanTime: DateTime.now(),
          status: BookScanStatus.failed,
          error: '获取书籍信息失败',
        ));
      }
    } catch (e) {
      debugPrint('获取书籍信息异常: $barcode, $e');
      
      // 发送错误结果
      _bookResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.failed,
        error: e.toString(),
      ));
    }
  }
  
  /// 开始模拟扫描（实际项目中删除）
  void _startMockScanning() {
    _mockScanTimer = Timer.periodic(Duration(milliseconds: 1500), (timer) {
      if (!_isScanning) {
        timer.cancel();
        return;
      }
      
      // 随机生成条码
      if (Random().nextDouble() > 0.4) { // 60%概率扫描到书籍
        final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
        _onBarcodeScanned(barcode);
      }
    });
  }
  
  /// 停止扫描
  Future<List<String>> stopScanning() async {
    if (!_isScanning) {
      debugPrint('增强RFID扫描未在进行中');
      return List.from(_scannedBarcodes);
    }
    
    try {
      debugPrint('停止增强RFID扫描');
      
      if (_readerConfigs.isNotEmpty) {
        // 停止真实扫描
        await ReaderManager.instance.stopInventory();
        
        // 移除监听器
        final tagProvider = Get.context?.read<HWTagProvider>();
        if (tagProvider != null) {
          tagProvider.removeListener(_onTagDataChanged);
        }
      } else {
        // 停止模拟扫描
        _stopMockScanning();
      }
      
      _isScanning = false;
      
      final result = List<String>.from(_scannedBarcodes);
      debugPrint('增强RFID扫描已停止，共扫描到${result.length}个条码');

      return result;
    } catch (e) {
      final errorMsg = '停止增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      return List<String>.from(_scannedBarcodes);
    }
  }
  
  /// 停止模拟扫描
  void _stopMockScanning() {
    _mockScanTimer?.cancel();
    _mockScanTimer = null;
  }
  
  /// 获取所有已扫描书籍的信息
  List<BookInfo> getAllScannedBooksInfo() {
    return _scannedBarcodes
        .where((barcode) => _bookInfoCache.containsKey(barcode))
        .map((barcode) => _bookInfoCache[barcode]!)
        .toList();
  }
  
  /// 获取当前扫描结果
  List<String> getCurrentScanResult() {
    return List<String>.from(_scannedBarcodes);
  }
  
  /// 清空扫描结果
  void clearScanResult() {
    _scannedBarcodes.clear();
    _bookInfoCache.clear();
    _processedBarcodes.clear();
    _countController.add(0);
    debugPrint('扫描结果已清空');
  }
  
  /// 连接健康检查
  bool _isConnectionHealthy(String cacheKey) {
    final lastUsed = _connectionLastUsed[cacheKey];
    final isHealthy = _connectionHealth[cacheKey] ?? false;
    
    if (!isHealthy || lastUsed == null) return false;
    
    // 连接超过5分钟未使用，认为可能不健康
    return DateTime.now().difference(lastUsed).inMinutes < 5;
  }
  
  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    _errorController.add(error);
  }
  
  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'scanning': _isScanning,
      'scanned_count': _scannedBarcodes.length,
      'book_info_count': _bookInfoCache.length,
      'reader_configs': _readerConfigs.length,
      'error': _errorMessage,
      'service_name': 'EnhancedRFIDService',
      'version': '1.0.0',
    };
  }
  
  /// 测试连接
  Future<bool> testConnection() async {
    try {
      if (_readerConfigs.isNotEmpty) {
        // 测试真实硬件连接
        await ReaderManager.instance.open();
        await ReaderManager.instance.close();
        return true;
      } else {
        // 模拟连接测试
        await Future.delayed(Duration(milliseconds: 100));
        return _isInitialized;
      }
    } catch (e) {
      debugPrint('连接测试失败: $e');
      return false;
    }
  }
  
  /// 重置服务
  Future<void> reset() async {
    debugPrint('重置增强RFID服务');
    
    if (_isScanning) {
      await stopScanning();
    }
    
    clearScanResult();
    _clearError();
  }
  
  /// 释放资源
  void dispose() {
    debugPrint('释放增强RFID服务资源');
    
    _stopMockScanning();
    _isScanning = false;
    _isInitialized = false;
    
    _barcodeController.close();
    _bookResultController.close();
    _countController.close();
    _errorController.close();
    
    debugPrint('增强RFID服务已释放');
  }
}

/// 书籍扫描结果
class BookScanResult {
  final String barcode;
  final BookInfo? bookInfo;
  final DateTime scanTime;
  final BookScanStatus status;
  final String? error;
  
  BookScanResult({
    required this.barcode,
    this.bookInfo,
    required this.scanTime,
    required this.status,
    this.error,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'book_info': bookInfo?.toJson(),
      'scan_time': scanTime.toIso8601String(),
      'status': status.toString(),
      'error': error,
    };
  }
}

/// 书籍扫描状态
enum BookScanStatus {
  processing, // 处理中
  success,    // 成功
  failed,     // 失败
}
