import 'package:flutter/material.dart';
import 'dart:async';

import '../services/enhanced_rfid_service.dart';
import '../services/book_info_api_service.dart';
import '../models/book_info.dart';
import '../widgets/enhanced_gate_scanning_widget.dart';

/// 🔥 增强书籍扫描测试页面
/// 用于测试和演示增强的RFID扫描功能
class EnhancedBookScanTestPage extends StatefulWidget {
  @override
  _EnhancedBookScanTestPageState createState() => _EnhancedBookScanTestPageState();
}

class _EnhancedBookScanTestPageState extends State<EnhancedBookScanTestPage> {
  final EnhancedRFIDService _rfidService = EnhancedRFIDService.instance;
  final BookInfoApiService _bookInfoService = BookInfoApiService.instance;
  
  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;
  String? _statusMessage;
  String? _errorMessage;
  
  // 扫描结果
  List<String> _scannedBarcodes = [];
  Map<String, BookInfo> _booksInfo = {};
  
  // 事件订阅
  StreamSubscription? _barcodeSubscription;
  StreamSubscription? _bookResultSubscription;
  StreamSubscription? _countSubscription;
  StreamSubscription? _errorSubscription;
  
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }
  
  @override
  void dispose() {
    _cancelSubscriptions();
    super.dispose();
  }
  
  /// 初始化服务
  Future<void> _initializeServices() async {
    try {
      setState(() {
        _statusMessage = '正在初始化服务...';
        _errorMessage = null;
      });
      
      // 初始化增强RFID服务
      await _rfidService.initialize();
      
      // 初始化书籍API服务
      _bookInfoService.initialize(
        baseUrl: 'http://localhost:3000', // 替换为实际API地址
      );
      
      // 设置事件监听
      _setupEventListeners();
      
      setState(() {
        _isInitialized = true;
        _statusMessage = '服务初始化完成';
      });
      
      print('增强书籍扫描测试页面初始化完成');
    } catch (e) {
      setState(() {
        _errorMessage = '初始化失败: $e';
        _statusMessage = null;
      });
      print('初始化失败: $e');
    }
  }
  
  /// 设置事件监听
  void _setupEventListeners() {
    // 监听条码流
    _barcodeSubscription = _rfidService.barcodeStream.listen(
      (barcode) {
        setState(() {
          if (!_scannedBarcodes.contains(barcode)) {
            _scannedBarcodes.add(barcode);
          }
        });
      },
    );
    
    // 监听书籍扫描结果流
    _bookResultSubscription = _rfidService.bookResultStream.listen(
      (result) {
        setState(() {
          if (result.bookInfo != null) {
            _booksInfo[result.barcode] = result.bookInfo!;
          }
        });
      },
    );
    
    // 监听计数流
    _countSubscription = _rfidService.countStream.listen(
      (count) {
        setState(() {
          _statusMessage = _isScanning ? '正在扫描... ($count)' : '扫描完成 ($count)';
        });
      },
    );
    
    // 监听错误流
    _errorSubscription = _rfidService.errorStream.listen(
      (error) {
        setState(() {
          _errorMessage = error;
        });
      },
    );
  }
  
  /// 取消事件订阅
  void _cancelSubscriptions() {
    _barcodeSubscription?.cancel();
    _bookResultSubscription?.cancel();
    _countSubscription?.cancel();
    _errorSubscription?.cancel();
  }
  
  /// 开始扫描
  Future<void> _startScanning() async {
    if (!_isInitialized || _isScanning) return;
    
    try {
      setState(() {
        _isScanning = true;
        _statusMessage = '正在启动扫描...';
        _errorMessage = null;
        _scannedBarcodes.clear();
        _booksInfo.clear();
      });
      
      await _rfidService.startEnhancedScanning();
      
      setState(() {
        _statusMessage = '扫描已启动';
      });
      
      print('增强RFID扫描已启动');
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = '启动扫描失败: $e';
      });
      print('启动扫描失败: $e');
    }
  }
  
  /// 停止扫描
  Future<void> _stopScanning() async {
    if (!_isScanning) return;
    
    try {
      setState(() {
        _statusMessage = '正在停止扫描...';
      });
      
      final result = await _rfidService.stopScanning();
      
      setState(() {
        _isScanning = false;
        _statusMessage = '扫描已停止，共扫描到 ${result.length} 个条码';
      });
      
      print('增强RFID扫描已停止，结果: ${result.length} 个条码');
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = '停止扫描失败: $e';
      });
      print('停止扫描失败: $e');
    }
  }
  
  /// 清空结果
  void _clearResults() {
    setState(() {
      _scannedBarcodes.clear();
      _booksInfo.clear();
      _errorMessage = null;
      _statusMessage = '结果已清空';
    });
    
    _rfidService.clearScanResult();
  }
  
  /// 手动添加测试条码
  void _addTestBarcode() {
    final testBarcodes = [
      'BOOK001',
      'BOOK002',
      'BOOK003',
      'BOOK004',
      'BOOK005',
    ];
    
    final barcode = testBarcodes[_scannedBarcodes.length % testBarcodes.length];
    
    setState(() {
      if (!_scannedBarcodes.contains(barcode)) {
        _scannedBarcodes.add(barcode);
      }
    });
    
    // 模拟获取书籍信息
    _bookInfoService.getBookInfo(barcode).then((bookInfo) {
      if (bookInfo != null) {
        setState(() {
          _booksInfo[barcode] = bookInfo;
        });
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('增强书籍扫描测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: _showServiceStatus,
            tooltip: '服务状态',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),
          
          // 状态显示
          _buildStatusPanel(),
          
          // 扫描结果显示
          Expanded(
            child: _buildScanResults(),
          ),
        ],
      ),
    );
  }
  
  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton.icon(
            onPressed: _isInitialized && !_isScanning ? _startScanning : null,
            icon: Icon(Icons.play_arrow),
            label: Text('开始扫描'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _isScanning ? _stopScanning : null,
            icon: Icon(Icons.stop),
            label: Text('停止扫描'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _clearResults,
            icon: Icon(Icons.clear_all),
            label: Text('清空结果'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _addTestBarcode,
            icon: Icon(Icons.add),
            label: Text('添加测试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建状态面板
  Widget _buildStatusPanel() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _errorMessage != null ? Colors.red.shade50 : Colors.blue.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Icon(
            _errorMessage != null ? Icons.error : 
            _isScanning ? Icons.radar : Icons.check_circle,
            color: _errorMessage != null ? Colors.red : 
                   _isScanning ? Colors.blue : Colors.green,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _errorMessage ?? _statusMessage ?? '准备就绪',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: _errorMessage != null ? Colors.red : Colors.black87,
                  ),
                ),
                if (_isInitialized)
                  Text(
                    '服务状态: ${_isScanning ? "扫描中" : "就绪"}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建扫描结果
  Widget _buildScanResults() {
    if (!_isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在初始化服务...'),
          ],
        ),
      );
    }
    
    return EnhancedGateScanningWidget(
      scannedBarcodes: _scannedBarcodes,
      booksInfo: _booksInfo,
      isScanning: _isScanning,
      statusMessage: _statusMessage,
    );
  }
  
  /// 显示服务状态
  void _showServiceStatus() {
    final rfidStatus = _rfidService.getStatus();
    final apiStatus = _bookInfoService.getStatus();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('服务状态'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('RFID服务状态:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...rfidStatus.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')),
              SizedBox(height: 16),
              Text('API服务状态:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...apiStatus.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
}
