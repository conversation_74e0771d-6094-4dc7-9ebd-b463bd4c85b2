# 轨道环绕动画修复

## 问题描述
扫描效果当前是绕圆心旋转，但应该是绕着一个40px半径的圆环轨道运动，创造真实的雷达扫描效果。

## 解决方案

### 修改前：圆心旋转
```dart
// 错误：整个扫描效果绕圆心旋转
Transform.rotate(
  angle: _rotationAnimation.value,
  child: Container(scanning_effect), // 整个容器旋转
);
```

### 修改后：轨道环绕
```dart
// 正确：扫描效果沿着40px半径的圆环轨道运动
AnimatedBuilder(
  animation: _rotationAnimation,
  builder: (context, child) {
    // 计算扫描效果在40px半径圆环上的位置
    final radius = 40.p; // 环绕半径
    final angle = _rotationAnimation.value;
    final offsetX = radius * math.cos(angle);
    final offsetY = radius * math.sin(angle);
    
    return Transform.translate(
      offset: Offset(offsetX, offsetY),
      child: Container(
        width: 60.p, // 扫描效果的大小
        height: 60.p,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('scanning_effect'),
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  },
)
```

## 技术实现

### 数学原理
使用三角函数计算圆周运动：
- `x = radius * cos(angle)` - 水平位置
- `y = radius * sin(angle)` - 垂直位置
- `angle` - 当前旋转角度（0到2π）

### 关键参数
- **环绕半径**: `40.p` - 扫描效果距离中心的距离
- **扫描效果大小**: `60.p × 60.p` - 扫描效果图片的显示尺寸
- **动画时长**: 3秒完整环绕一圈

### 坐标系统
```
        ↑ Y轴
        │
        │    ● 扫描效果 (radius=40px)
        │   ╱
        │  ╱
        │ ╱ angle
────────●────────→ X轴
        │ 中心点
        │
        │
```

## 视觉效果对比

### 修改前（圆心旋转）
```
┌─────────────────┐
│       ●         │ ← 扫描效果绕中心旋转
│   ╭───┼───╮     │
│  ╱    │    ╲    │
│ │     ●     │   │ ← 中心点
│  ╲    │    ╱    │
│   ╰───┼───╯     │
│       │         │
└─────────────────┘
```

### 修改后（轨道环绕）
```
┌─────────────────┐
│                 │
│   ╭─────────╮   │
│  ╱     ●     ╲  │ ← 扫描效果沿圆环运动
│ │      3      │ │ ← 数字在中心
│  ╲           ╱  │
│   ╰─────────╯   │
│                 │
└─────────────────┘
```

## 图层结构

### 完整的Stack结构
1. **底层**: `radar_scan_bg` - 静态雷达底图（280×280px）
2. **中层**: `scanning_effect` - 环绕运动的扫描效果（60×60px）
3. **顶层**: 数字文字 - 静态显示在中心

### 动画效果
- ✅ **radar_scan_bg**: 保持静态
- 🔄 **scanning_effect**: 沿着40px半径的圆环轨道运动
- ✅ **数字文字**: 保持静态在中心

## 代码变更

### 新增导入
```dart
import 'dart:math' as math; // 用于三角函数计算
```

### 动画逻辑
- 使用`Transform.translate`代替`Transform.rotate`
- 通过`Offset(offsetX, offsetY)`控制位置
- 扫描效果大小调整为60×60px

## 参数调整

### 可调整的参数
- **环绕半径**: `40.p` - 可以调整轨道大小
- **扫描效果尺寸**: `60.p` - 可以调整扫描点大小
- **动画速度**: 当前3秒一圈，可以调整

### 建议的参数范围
- 环绕半径: 30-60px（根据底图圆环大小）
- 扫描效果尺寸: 40-80px（保持合适的视觉比例）

## 总结
现在扫描效果会沿着40px半径的圆环轨道运动，创造了更真实的雷达扫描视觉效果。扫描点不再是简单的旋转，而是真正的轨道环绕运动。
